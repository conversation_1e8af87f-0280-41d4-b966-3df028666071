package com.yingfei.entity.dto.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 月度能力趋势报表返回DTO
 * <AUTHOR>
 */
@Data
@ApiModel("月度能力趋势报表返回DTO")
@Accessors(chain = true)
public class MonthTrendResultDTO {

    @ApiModelProperty("产品ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long partId;

    @ApiModelProperty("产品名称")
    private String partName;

    @ApiModelProperty("产品版本ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long ptrvId;

    @ApiModelProperty("产品版本名称")
    private String ptrvName;

    @ApiModelProperty("过程ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long prcsId;

    @ApiModelProperty("过程名称")
    private String prcsName;

    @ApiModelProperty("测试ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long testId;

    @ApiModelProperty("测试名称")
    private String testName;

    @ApiModelProperty("公差上限(USL)")
    private Double usl;

    @ApiModelProperty("目标值(TAR)")
    private Double tar;

    @ApiModelProperty("公差下限(LSL)")
    private Double lsl;

    @ApiModelProperty("目标CPK")
    private Double targetCpk;

    @ApiModelProperty("各月份子组数量 - key为月份(yyyy-MM)，value为子组数量")
    private Map<String, Integer> monthlySubgroupCount;

    @ApiModelProperty("各月份CPK值 - key为月份(yyyy-MM)，value为CPK值")
    private Map<String, Double> monthlyCpk;

    @ApiModelProperty("月改善幅度(%)")
    private BigDecimal improvementRate;

    @ApiModelProperty("能力分析结果 1：满足 2：较差 3：严重不足 4：极差")
    private Integer capabilityAnalysis;

    @ApiModelProperty("最新月份")
    private String latestMonth;

    @ApiModelProperty("最新月份子组数量")
    private Integer latestSubgroupCount;

    @ApiModelProperty("最新月份CPK")
    private Double latestCpk;

    @ApiModelProperty("上月CPK")
    private Double previousCpk;
    @ApiModelProperty("项目负责人信息")
    private List<ResponsiblePersonInfo> responsiblePerson;

    @Data
    @Accessors(chain = true)
    public static class ResponsiblePersonInfo {
        @ApiModelProperty("项目负责人ID")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private final Long personId;
        @ApiModelProperty("项目负责人名称")
        private final String personName;
        public ResponsiblePersonInfo(Long personId, String personName) {
            this.personId = personId;
            this.personName = personName;
        }

        public Long getPersonId() { return personId; }
        public String getPersonName() { return personName; }
    }

    /**
     * 计算改善幅度
     * 公式：(最新月CPK - 上月CPK) / 上月CPK * 100
     */
    public void calculateImprovementRate() {
        if (latestCpk != null && previousCpk != null && previousCpk != 0) {
            double rate = (latestCpk - previousCpk) / previousCpk * 100;
            this.improvementRate = BigDecimal.valueOf(rate).setScale(2, BigDecimal.ROUND_HALF_UP);
        }
    }

    /**
     * 分析能力等级
     * CPK >= 1.33 = 满足
     * 1 <= CPK < 1.33 = 较差
     * 0.67 <= CPK < 1 = 严重不足
     * CPK < 0.67 = 极差
     */
    public void analyzeCapability() {
        if (latestCpk == null) {
            this.capabilityAnalysis = null;
            return;
        }

        if (latestCpk >= 1.33) {
            this.capabilityAnalysis = 1;
        } else if (latestCpk >= 1.0) {
            this.capabilityAnalysis = 2;
        } else if (latestCpk >= 0.67) {
            this.capabilityAnalysis = 3;
        } else {
            this.capabilityAnalysis = 4;
        }
    }
}
