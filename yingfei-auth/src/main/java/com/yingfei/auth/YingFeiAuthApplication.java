package com.yingfei.auth;

import com.yingfei.common.security.annotation.EnableCustomConfig;
import com.yingfei.common.security.annotation.EnableRyFeignClients;
import com.yingfei.common.swagger.annotation.EnableCustomSwagger2;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;

import java.util.TimeZone;

/**
 * 认证授权中心
 */
@EnableCustomConfig
@EnableCustomSwagger2
@EnableRyFeignClients
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@ComponentScan(basePackages = {"com.yingfei"})
public class YingFeiAuthApplication {
    public static void main(String[] args) {
        TimeZone.setDefault(TimeZone.getTimeZone("UTC"));
        SpringApplication.run(YingFeiAuthApplication.class, args);
        System.out.println("认证授权中心启动成功");
    }
}
