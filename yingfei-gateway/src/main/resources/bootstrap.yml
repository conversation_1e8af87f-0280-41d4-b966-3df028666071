# Tomcat
server:
  port: 8080

profile: ${ENV:pgsql}

# Spring
spring:
  application:
    # 应用名称
    name: yingfei-gateway
  cloud:
    nacos:
      common:
        # 服务注册地址
        server-addr: 192.168.2.13:8848
        # 命名空间
        namespace: qdd-test
        #用户名
        username: nacos
        #密码
        password: nacos
      discovery:
        server-addr: ${spring.cloud.nacos.common.server-addr}
        namespace: ${spring.cloud.nacos.common.namespace}
        username: ${spring.cloud.nacos.common.username}
        password: ${spring.cloud.nacos.common.password}
      config:
        server-addr: ${spring.cloud.nacos.common.server-addr}
        namespace: ${spring.cloud.nacos.common.namespace}
        username: ${spring.cloud.nacos.common.username}
        password: ${spring.cloud.nacos.common.password}
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - data-id: datasource-${profile}.yml
            group: DEFAULT_GROUP
            refresh: true
        # 应用特定配置
        extension-configs:
          - data-id: yingfei-gateway.yml
            group: DEFAULT_GROUP
            refresh: true
    sentinel:
      # 取消控制台懒加载
      eager: true
      transport:
        # 控制台地址
        dashboard: 127.0.0.1:8718
      # nacos配置持久化
      datasource:
        ds1:
          nacos:
            #           server-addr: 171.223.207.66:8282
            server-addr: 192.168.2.13:8848
            namespace: spc
            username: nacos
            password: nacos
            dataId: sentinel-yingfei-gateway
            groupId: DEFAULT_GROUP
            data-type: json
            rule-type: gw-flow
