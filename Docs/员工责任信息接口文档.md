# 员工责任信息接口文档

## 概述

员工责任信息管理API，用于管理员工与测试项目的责任映射关系。主要功能包括员工责任信息的增删改查操作。

**基础路径**: `/emplResponsibleInf`

**API标签**: 员工责任信息API

---

## 数据模型

### EMPL_RESPONSIBLE_INF_DTO (响应数据传输对象)

| 字段名 | 类型 | 描述 | 示例 |
|--------|------|------|------|
| F_RESP | Long | 主键ID | "1234567890123456789" |
| F_TYPE | Short | 业务类型 (0=员工与测试映射) | 0 |
| F_EMPL | Long | 员工ID | "9876543210987654321" |
| F_DATA | String | 映射业务数据 (当F_TYPE=0时为测试ID) | "1111111111111111111" |
| F_DEL | Integer | 删除标记 (0=未删除, 1=已删除) | 0 |
| F_CRUE | Long | 创建用户ID | "1234567890123456789" |
| F_EDUE | Long | 编辑用户ID | "1234567890123456789" |
| F_CRTM | Date | 创建时间 | "2024-01-01 10:00:00" |
| F_EDTM | Date | 编辑时间 | "2024-01-01 15:30:00" |
| emplName | String | 员工姓名 | "张三" |
| F_TEST | Long | 测试ID | "1111111111111111111" |
| testName | String | 测试名称 | "温度测试" |

### EMPL_RESPONSIBLE_INF_VO (请求数据传输对象)

| 字段名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| F_RESP | Long | 否 | 主键ID (编辑时必填) | "1234567890123456789" |
| F_TYPE | Integer | 是 | 业务类型 (0=员工与测试映射) | 0 |
| F_EMPL | Long | 是 | 员工ID | "9876543210987654321" |
| F_DATA | String | 是 | 映射业务数据 | "1111111111111111111" |
| F_CRUE | Long | 否 | 创建用户ID | "1234567890123456789" |
| F_EDUE | Long | 否 | 编辑用户ID | "1234567890123456789" |
| emplIds | List<Long> | 否 | 员工ID列表 (批量查询用) | ["123", "456"] |
| testIds | List<Long> | 否 | 测试ID列表 (批量查询用) | ["111", "222"] |
| ids | List<Long> | 否 | 主键ID列表 (批量删除用) | ["789", "101"] |

### 通用响应格式

#### R<T> (标准响应格式)
```json
{
  "code": 200,
  "message": "操作成功",
  "data": T
}
```

#### TableDataInfo (分页响应格式)
```json
{
  "total": 100,
  "rows": [],
  "code": 200,
  "message": "查询成功"
}
```

---

## 接口列表

### 1. 获取员工责任信息列表

**接口描述**: 分页查询员工责任信息列表

**请求信息**:
- **URL**: `POST /emplResponsibleInf/list`
- **权限**: `system:emplResponsibleInf:list`
- **Content-Type**: `application/json`

**请求参数**:
```json
{
  "F_TYPE": 0,
  "F_EMPL": "9876543210987654321",
  "emplIds": ["123", "456"],
  "testIds": ["111", "222"],
  "F_DATA": "1111111111111111111"
}
```

**响应示例**:
```json
{
  "total": 50,
  "rows": [
    {
      "F_RESP": "1234567890123456789",
      "F_TYPE": 0,
      "F_EMPL": "9876543210987654321",
      "F_DATA": "1111111111111111111",
      "F_DEL": 0,
      "F_CRUE": "1234567890123456789",
      "F_EDUE": "1234567890123456789",
      "F_CRTM": "2024-01-01 10:00:00",
      "F_EDTM": "2024-01-01 15:30:00",
      "emplName": "张三",
      "F_TEST": "1111111111111111111",
      "testName": "温度测试"
    }
  ],
  "code": 200,
  "message": "查询成功"
}
```

### 2. 根据员工ID或测试ID获取列表

**接口描述**: 根据员工ID或测试ID获取责任信息列表

**请求信息**:
- **URL**: `POST /emplResponsibleInf/getListByFEmplOrFTest`
- **权限**: `system:emplResponsibleInf:list`
- **Content-Type**: `application/json`

**请求参数**:
```json
{
  "F_TYPE": 0,
  "F_EMPL": "9876543210987654321",
  "testIds": ["1111111111111111111", "2222222222222222222"]
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "F_RESP": "1234567890123456789",
      "F_TYPE": 0,
      "F_EMPL": "9876543210987654321",
      "F_DATA": "1111111111111111111",
      "emplName": "张三",
      "F_TEST": "1111111111111111111",
      "testName": "温度测试"
    }
  ]
}
```

### 3. 根据员工ID获取责任信息

**接口描述**: 根据员工ID获取该员工负责的所有测试项目

**请求信息**:
- **URL**: `GET /emplResponsibleInf/test/getByFEmpl/{fEmpl}`
- **权限**: `system:emplResponsibleInf:query`

**路径参数**:
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| fEmpl | Long | 是 | 员工ID | 9876543210987654321 |

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "F_RESP": "1234567890123456789",
      "F_TYPE": 0,
      "F_EMPL": "9876543210987654321",
      "F_DATA": "1111111111111111111",
      "emplName": "张三",
      "F_TEST": "1111111111111111111",
      "testName": "温度测试"
    }
  ]
}
```

### 4. 根据测试ID获取负责员工信息

**接口描述**: 根据测试ID获取负责该测试的员工信息

**请求信息**:
- **URL**: `GET /emplResponsibleInf/test/getByFTest/{fTest}`
- **权限**: `system:emplResponsibleInf:query`

**路径参数**:
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| fTest | Long | 是 | 测试ID | 1111111111111111111 |

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "F_RESP": "1234567890123456789",
      "F_TYPE": 0,
      "F_EMPL": "9876543210987654321",
      "F_DATA": "1111111111111111111",
      "emplName": "张三",
      "F_TEST": "1111111111111111111",
      "testName": "温度测试"
    }
  ]
}
```

### 5. 新增员工责任信息

**接口描述**: 创建新的员工责任信息映射关系

**请求信息**:
- **URL**: `POST /emplResponsibleInf/add`
- **权限**: `system:emplResponsibleInf:add`
- **Content-Type**: `application/json`

**请求参数**:
```json
{
  "F_TYPE": 0,
  "F_EMPL": "9876543210987654321",
  "F_DATA": "1111111111111111111"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": null
}
```

### 6. 编辑员工责任信息

**接口描述**: 更新现有的员工责任信息

**请求信息**:
- **URL**: `POST /emplResponsibleInf/edit`
- **权限**: `system:emplResponsibleInf:edit`
- **Content-Type**: `application/json`

**请求参数**:
```json
{
  "F_RESP": "1234567890123456789",
  "F_TYPE": 0,
  "F_EMPL": "9876543210987654321",
  "F_DATA": "2222222222222222222"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": null
}
```

### 7. 删除员工责任信息

**接口描述**: 批量删除员工责任信息 (逻辑删除)

**请求信息**:
- **URL**: `POST /emplResponsibleInf/del`
- **权限**: `system:emplResponsibleInf:remove`
- **Content-Type**: `application/json`

**请求参数**:
```json
["1234567890123456789", "9876543210987654321"]
```

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": null
}
```

---

## 业务规则

### 数据约束
1. **唯一性约束**: 当F_TYPE=0时，一个测试只能有一个负责人 (F_DATA字段存储测试ID)
2. **必填字段**: F_TYPE、F_EMPL、F_DATA为必填字段
3. **员工状态**: 只能为激活状态的员工分配责任

### 业务逻辑
1. **员工与测试映射**: 当F_TYPE=0时，F_DATA字段直接存储测试ID
2. **逻辑删除**: 删除操作为逻辑删除，设置F_DEL=1
3. **权限控制**: 所有接口都需要相应的权限验证
4. **审计日志**: 新增、编辑、删除操作会记录操作日志

### 错误处理
- **参数缺失**: 返回参数缺失异常
- **数据重复**: 返回数据已存在异常  
- **权限不足**: 返回权限不足异常
- **数据不存在**: 返回数据不存在异常

---

## 状态码说明

| 状态码 | 描述 | 说明 |
|--------|------|------|
| 200 | 成功 | 操作成功 |
| 400 | 参数错误 | 请求参数不正确 |
| 401 | 未授权 | 用户未登录或token无效 |
| 403 | 权限不足 | 用户没有相应操作权限 |
| 500 | 服务器错误 | 系统内部错误 |

---

## 注意事项

1. **ID格式**: 所有ID字段使用Long类型，JSON传输时转为字符串格式
2. **时间格式**: 时间字段统一使用 "yyyy-MM-dd HH:mm:ss" 格式
3. **分页查询**: list接口支持分页，通过继承BaseController实现
4. **权限验证**: 所有接口都需要相应的权限，请确保用户具有对应权限
5. **数据完整性**: 删除操作为逻辑删除，不会物理删除数据

---

## 常见问题解决

### I18nUtils 未初始化错误

**错误信息**: `java.lang.IllegalStateException: I18nUtils 未初始化，请先调用 init() 方法注入 MessageSource`

**解决方案**:

1. **确保配置类被扫描**: 在启动类中添加 `@ComponentScan` 注解，确保扫描到 `I18nConfig` 配置类
2. **检查MessageSource配置**: 确保 `I18nConfig` 类正确配置了 `MessageSource` Bean
3. **验证资源文件路径**: 确保国际化资源文件存在于 `classpath:i18n/` 目录下
4. **检查依赖注入**: 确保 `I18nInitializer` 组件被正确加载

**预防措施**:
- 在应用启动时会自动初始化 `I18nUtils`
- 如果初始化失败，系统会降级使用默认消息而不是抛出异常

---

*文档生成时间: 2024-08-04*
*API版本: v1.0*
