package com.yingfei.common.security.aspect;

import com.yingfei.common.core.utils.I18nUtils;
import com.yingfei.common.security.annotation.InnerAuth;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;
import com.yingfei.common.core.constant.SecurityConstants;
import com.yingfei.common.core.exception.InnerAuthException;
import com.yingfei.common.core.utils.ServletUtils;
import com.yingfei.common.core.utils.StringUtils;

/**
 * 内部服务调用验证处理
 */
@Aspect
@Component
public class InnerAuthAspect implements Ordered
{
    @Around("@annotation(innerAuth)")
    public Object innerAround(ProceedingJoinPoint point, InnerAuth innerAuth) throws Throwable
    {
        String source = ServletUtils.getRequest().getHeader(SecurityConstants.FROM_SOURCE);
        // 内部请求验证
        if (!StringUtils.equals(SecurityConstants.INNER, source))
        {
            throw new InnerAuthException(I18nUtils.getMessage("NO_INTERNAL_ACCESS_PERMISSION_ACCESS_NOT_ALLOWED"));
        }

        String userid = ServletUtils.getRequest().getHeader(SecurityConstants.DETAILS_USER_ID);
        String username = ServletUtils.getRequest().getHeader(SecurityConstants.DETAILS_USERNAME);
        String phonenumber = ServletUtils.getRequest().getHeader(SecurityConstants.DETAILS_PHONE);
        // 用户信息验证
        if (innerAuth.isUser() && (StringUtils.isEmpty(userid) || StringUtils.isEmpty(username) || StringUtils.isEmpty(phonenumber)))
        {
            throw new InnerAuthException(I18nUtils.getMessage("NO_USER_INFO_SET_ACCESS_NOT_ALLOWED"));
        }
        return point.proceed();
    }

    /**
     * 确保在权限认证aop执行前执行
     */
    @Override
    public int getOrder()
    {
        return Ordered.HIGHEST_PRECEDENCE + 1;
    }
}
