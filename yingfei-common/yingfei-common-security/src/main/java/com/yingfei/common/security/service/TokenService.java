package com.yingfei.common.security.service;

import com.yingfei.common.core.constant.CacheConstants;
import com.yingfei.common.core.constant.SecurityConstants;
import com.yingfei.common.core.enums.LoginTypeEnum;
import com.yingfei.common.core.exception.base.BaseException;
import com.yingfei.common.core.utils.AESUtil;
import com.yingfei.common.core.utils.JwtUtils;
import com.yingfei.common.core.utils.ServletUtils;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.core.utils.ip.IpUtils;
import com.yingfei.common.core.utils.uuid.IdUtils;
import com.yingfei.common.redis.configure.RedisConstant;
import com.yingfei.common.redis.service.RedisService;
import com.yingfei.common.security.utils.SecurityUtils;
import com.yingfei.entity.dto.EMPL_INF_DTO;
import com.yingfei.entity.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;


/**
 * token验证处理
 */
@Component
public class TokenService {
    @Autowired
    private RedisService redisService;

    protected static final long MILLIS_SECOND = 1000;

    protected static final long MILLIS_MINUTE = 60 * MILLIS_SECOND;

    private final static long expireTime = CacheConstants.EXPIRATION;

//    private final static String ACCESS_TOKEN = CacheConstants.LOGIN_TOKEN_KEY;

    private final static Long MILLIS_MINUTE_TEN = CacheConstants.REFRESH_TIME * MILLIS_MINUTE;

    /**
     * 创建令牌
     */
    public Map<String, Object> createToken(LoginUser loginUser) {
        EMPL_INF_DTO sysUser = loginUser.getSysUser();
        Long userId = sysUser.getF_EMPL();

        LoginTypeEnum loginType = LoginTypeEnum.PC;
        String ipAddr = IpUtils.getIpAddr();
        String key = CacheConstants.LOGIN_TOKEN_KEY + "*";
        Collection<String> keys = redisService.keys(key);
        Object o = redisService.get(RedisConstant.DATA_MANAGEMENT_SERVICE);
        int i = 20;
        if (o != null) {
            String decrypt = AESUtil.decryptStr(o.toString(), AESUtil.defaultAesKey);
            i = Integer.parseInt(decrypt == null ? "20" : decrypt.split("@")[1]);
        }

        if (i != -1 && keys.size() == i) {
            throw new BaseException("该系统登录人数已达上限!");
        }
        String token = IdUtils.fastUUID();
        String userName = sysUser.getF_NAME();
        loginUser.setToken(token);
        loginUser.setUserid(userId);
        loginUser.setUsername(userName);
        loginUser.setIpaddr(ipAddr);
        loginUser.setLoginType(loginType.getType());
        refreshToken(loginUser);

        // Jwt存储信息
        Map<String, Object> claimsMap = new HashMap<String, Object>();
        claimsMap.put(SecurityConstants.USER_KEY, token);
        claimsMap.put(SecurityConstants.DETAILS_USER_ID, userId);
        claimsMap.put(SecurityConstants.DETAILS_USERNAME, userName);

        // 接口返回信息
        Map<String, Object> rspMap = new HashMap<String, Object>();
        rspMap.put("access_token", JwtUtils.createToken(claimsMap));
        rspMap.put("expires_in", expireTime);
        return rspMap;
    }

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginUser getLoginUser() {
        return getLoginUser(ServletUtils.getRequest());
    }

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginUser getLoginUser(HttpServletRequest request) {
        // 获取请求携带的令牌
        String token = SecurityUtils.getToken(request);
        return getLoginUser(token);
    }

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginUser getLoginUser(String token) {
        LoginUser user = null;
        try {
            if (StringUtils.isNotEmpty(token)) {
                String userkey = JwtUtils.getUserKey(token);
                String userId = JwtUtils.getUserId(token);
                user = redisService.getCacheObject(getTokenKey(userkey, userId));
                return user;
            }
        } catch (Exception e) {
        }
        return user;
    }

    /**
     * 设置用户身份信息
     */
    public void setLoginUser(LoginUser loginUser) {
        if (StringUtils.isNotNull(loginUser) && StringUtils.isNotEmpty(loginUser.getToken())) {
            refreshToken(loginUser);
        }
    }

    /**
     * 删除用户缓存信息
     */
    public void delLoginUser(String token) {
        if (StringUtils.isNotEmpty(token)) {
            String userkey = JwtUtils.getUserKey(token);
            String userId = JwtUtils.getUserId(token);
            redisService.deleteObject(getTokenKey(userkey, userId));
        }
    }

    /**
     * 验证令牌有效期，相差不足120分钟，自动刷新缓存
     *
     * @param loginUser
     */
    public void verifyToken(LoginUser loginUser) {
        long expireTime = loginUser.getExpireTime();
        long currentTime = System.currentTimeMillis();
        if (expireTime - currentTime <= MILLIS_MINUTE_TEN) {
            refreshToken(loginUser);
        }
    }

    /**
     * 刷新令牌有效期
     *
     * @param loginUser 登录信息
     */
    public void refreshToken(LoginUser loginUser) {
        loginUser.setLoginTime(System.currentTimeMillis());
        loginUser.setExpireTime(loginUser.getLoginTime() + expireTime * MILLIS_MINUTE);
        // 根据uuid将loginUser缓存
        String userKey = getTokenKey(loginUser.getToken(), String.valueOf(loginUser.getUserid()));
        redisService.setCacheObject(userKey, loginUser, expireTime, TimeUnit.MINUTES);
    }

    private String getTokenKey(String token, String userId) {
        return redisService.getUserTokenKey(token, userId, SecurityUtils.getUserAgent());
//        return ACCESS_TOKEN + token;
    }
}
