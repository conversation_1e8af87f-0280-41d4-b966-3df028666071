#validate Messages:
system_error_please_contact_the_administrator=System error, please contact the administrator
sys_exception=System exception
service_not_found=Service not found
the_user_has_no_permission=The user does not have permission for this function

# BpmProcessInstanceDeleteReasonEnum
REJECT_TASK = Reject the task, reason: {}
CANCEL_TASK = Actively cancel the task, reason: {}
MULTI_TASK_END = Automatically cancelled by the system, reason: Multi - task approval has met the conditions, no need to approve this task 

# BpmProcessInstanceResultEnum
PROCESS = In progress
APPROVE = Approved
REJECT = Rejected
CANCEL = Cancelled
BACK = Returned/Rejected

# BpmProcessInstanceStatusEnum
RUNNING = Running
FINISH = Completed

# ExceptionRecordEnum
PENDING_APPROVAL = Pending approval
ALREADY_PASSED = Already passed
WITHDRAWN = Withdrawn
DECLINED = Declined

# ExceptionStatusEnum
ASK_FOR_LEAVE = Leave application
GO_OUT = Outgoing application
ABNORMAL_FILLING = Abnormal card - filling processing application
OTHER = Other applications

# AuthExceptionEnum
AUTH_PERMISS_EXCEPTION = Insufficient permissions
AUTH_MARK_DUPLICATION_EXCEPTION = Permission identifier duplication
AUTH_PASSWORD_EXCEPTION = Incorrect login password
AUTH_ACCOUNT_EXPIRED_EXCEPTION = This account has expired
AUTH_USER_NOT_LOGIN_EXCEPTION = User not logged in
IP_BLACKLIST = This IP has been added to the system blacklist
LOGIN_ACCOUNT_DOES_NOT_EXIST = Login account does not exist
AUTH_ACCOUNT_PASSWORD_EMPTY_EXCEPTION = Mobile number/password must be filled
LOGIN_ACCOUNT_UP_TO_THE_LIMIT = The number of failed login attempts for this user has reached the limit. Please try again after 1 hour!
LOGIN_USER_COUNT_REACH_LIMIT = The number of logged - in users of this system has reached the limit
# CommonExceptionEnum
PARAMETER_MISSING_EXCEPTION = Request parameter missing
PRESENCE_SUBDATA__EXCEPTION = This group has sub - data
DATA_NOT_FOUND_EXCEPTION = This data does not exist
IMPORT_DATA_NOT_NULL_EXCEPTION = Import data cannot be empty
MQ_SEND_EXCEPTION = Message sending failed
DO_NOT_SUBMIT_DATA_TWICE = Do not submit data twice
ACCESS_TOKEN_FAILED = Failed to get access_token
SEND_CONFIG_NOT_EXIST = Message sending configuration is not set
THE_IMPORTED_FILE_TYPE_IS_NOT_SUPPORTED = The imported file type is not supported
FEIGN_ERROR = Feign call failed
DATA_ALREADY_EXISTS_EXCEPTION = Data already exists
UNSUPPORTED_DATABASE_TYPE_EXCEPTION = Unsupported database type

# DataCollectionExceptionEnum
NUMBER_OF_COLUMNS_DOES_NOT_MATCH_EXCEPTION = The number of columns in the imported file does not match. Please check the file format
START_LINE_EXCEPTION = Please check if the start line is correct
TASK_TIME_NOT_REACHED = The task has not reached the start time and cannot be resumed

# DataManagementExceptionEnum
PRODUCT_NAME_DUPLICATION_EXCEPTION = Product name duplication
PROCESS_NAME_DUPLICATION_EXCEPTION = Process name duplication
TEST_NAME_DUPLICATION_EXCEPTION = Test name duplication
TAG_GRP_NAME_DUPLICATION_EXCEPTION = Tag group name duplication
TAG_NAME_DUPLICATION_EXCEPTION = Tag name duplication
SHIFT_GRP_NAME_DUPLICATION_EXCEPTION = Shift group name duplication
SHIFT_NAME_DUPLICATION_EXCEPTION = Shift name duplication
PRODUCT_BATCH_NAME_DUPLICATION_EXCEPTION = Product batch name duplication
PRODUCT_SERIAL_NUMBER_NAME_DUPLICATION_EXCEPTION = Product serial number name duplication
JOB_GRP_NAME_DUPLICATION_EXCEPTION = Work order group name duplication
JOB_NAME_DUPLICATION_EXCEPTION = Work order name duplication
DESC_GRP_NAME_DUPLICATION_EXCEPTION = Custom descriptor group name duplication
DESC_NAME_DUPLICATION_EXCEPTION = Custom descriptor name duplication
DEF_GRP_NAME_DUPLICATION_EXCEPTION = Defect code group name duplication
DEF_NAME_DUPLICATION_EXCEPTION = Defect code name duplication
ROOT_CAUSE_GRP_NAME_DUPLICATION_EXCEPTION = Root cause group name duplication
ROOT_CAUSE_NAME_DUPLICATION_EXCEPTION = Root cause name duplication
RESPONSE_ACTION_GRP_NAME_DUPLICATION_EXCEPTION = Improvement measure group name duplication
RESPONSE_ACTION_NAME_DUPLICATION_EXCEPTION = Improvement measure name duplication
PARAMETER_SET_NAME_DUPLICATION_EXCEPTION = Parameter set name duplication
PROCESSING_TEMPLATE_NAME_DUPLICATION_EXCEPTION = Data processing template name duplication
ACTIVED_RULE_TEMPLATE_NAME_DUPLICATION_EXCEPTION = Alarm rule template name duplication
WARNING_LIMIT_EXCEPTION = Alarm limit configuration error
REASONABLE_LIMIT_EXCEPTION = Reasonable limit configuration error

USL_LSL_EXCEPTION = Tolerance limit configuration error
PRODUCT_REV_NAME_DUPLICATION_EXCEPTION = Product version name duplication
MANUFACTURING_PROCESS_NAME_DUPLICATION_EXCEPTION = Flowchart structure name duplication
DELETE_THE_SUBPLAN_FIRST_EXCEPTION = Please delete the sub - plan first
CLEAR_THE_CACHE_SUBGROUP_EXCEPTION = Failed to clear the cache subgroup
PROCESS_DEFINITION_KEY_NOT_MATCH = The expected identifier of the process definition is ({}), but the current one is ({}). Please modify the BPMN flowchart
PROCESS_DEFINITION_NAME_NOT_MATCH = The expected name of the process definition is ({}), but the current one is ({}). Please modify the BPMN flowchart
MODEL_KEY_VALID = The process identifier format is incorrect. It needs to start with a letter or underscore, followed by any letters, numbers, hyphens, underscores, or periods!
MODEL_KEY_EXISTS = There is already a process with the identifier [{}]
MODEL_NOT_EXISTS = The process model does not exist
MODEL_DEPLOY_FAIL_FORM_NOT_CONFIG = Failed to deploy the process. Reason: The process form is not configured. Please click the "Modify Process" button to configure.
FORM_NOT_EXISTS = The dynamic form does not exist
PROCESS_DEFINITION_NOT_EXISTS = The process definition does not exist
PROCESS_INSTANCE_NOT_EXISTS = The process instance does not exist
TASK_COMPLETE_FAIL_NOT_EXISTS = Failed to approve the task. Reason: The task is not in the unapproved state
TASK_COMPLETE_FAIL_ASSIGN_NOT_SELF = Failed to approve the task. Reason: The approver of this task is not you
PROCESS_DEFINITION_IS_SUSPENDED = The process definition is suspended
PROCESS_INSTANCE_CANCEL_FAIL_NOT_EXISTS = Failed to cancel the process. The process is not running
PROCESS_INSTANCE_CANCEL_FAIL_NOT_SELF = Failed to cancel the process. This process was not initiated by you
TASK_ASSIGN_RULE_EXISTS = The assignment rule already exists for task ({}) in process ({})
TASK_ASSIGN_RULE_NOT_EXISTS = The process task does not exist
TASK_UPDATE_FAIL_NOT_MODEL = Only the task assignment rules of the process model can be modified
MODEL_DEPLOY_FAIL_TASK_ASSIGN_RULE_NOT_CONFIG = Failed to deploy the process. Reason: The assignment rule for user task ({}) is not configured. Please click the "Modify Process" button to configure.
TASK_ASSIGN_FORM_EXISTS = The assignment form already exists for task ({}) in process ({})
TASK_CREATE_FAIL_NO_CANDIDATE_USER = Operation failed. Reason: The approver for the task cannot be found!
GAUGE_AGENT_NAME_DUPLICATION_EXCEPTION = Gauge Agent name or hardware ID already exists
GAUGE_HARDWARE_ID_NOT_EXISTS = Gauge hardware ID does not exist
GAUGE_FORMAT_NAME_DUPLICATION_EXCEPTION = Gauge parsing rule name duplication
GAUGE_INTERFACE_NAME_DUPLICATION_EXCEPTION = Gauge interface configuration name duplication
GAUGE_CONNECTION_NOT_EXISTS = Gauge connection configuration does not exist
GAUGE_DEVICE_NOT_EXISTS = Gauge device does not exist
GAUGE_FORMAT_NOT_EXISTS = Gauge parsing configuration does not exist
PLEASE_CHECK_THE_TERMINAL_CONFIGURATION = Please check the terminator configuration
TERMINAL_SYMBOL_NOT_EXISTS = No terminator exists
PLEASE_CHECK_THE_INITIATOR_CONFIGURATION = Please check the initiator configuration
THE_CHANNEL_NUMBER_DOES_NOT_MATCH = The channel number does not match
CTRL_DUPLICATION_EXCEPTION = Control limit duplication
SPEC_DUPLICATION_EXCEPTION = Tolerance limit duplication
PARAMETER_SET_NOT_EXISTS = Parameter set does not exist
MENU_ALREADY_ASSOCIATED_ANALYSIS_TEMPLATE = The menu is already associated with an analysis template
CHART_TYPE_NOT_EXISTS = Chart type does not exist
QUADRANT_CHART_CONFIG_NOT_EXISTS = Quadrant chart configuration does not exist
ANALYSIS_DASHBOARD_NOT_EXISTS = Analysis page does not exist
CHART_CONFIG_NOT_EXISTS = Chart configuration does not exist
DICT_CODE_DUPLICATION_EXCEPTION = Dictionary identifier and content duplication
JOB_GRP_NOT_EXISTS = Work group does not exist
SHIFT_GRP_NOT_EXISTS = Shift group does not exist
DESC_GRP_NOT_EXISTS = Descriptor group does not exist
DES_GRP_NOT_EXISTS = Defect code group does not exist
MENU_NOT_ASSOCIATED_PARAMETER_SET = The menu is not associated with a parameter set
DEF_GRP_NOT_ALLOW_CREATE_DEF = This defect code group does not allow creating defect codes
DESC_GRP_NOT_ALLOW_CREATE_DESC = This descriptor group does not allow creating descriptors
JOB_GRP_NOT_ALLOW_CREATE_JOB = This work group does not allow creating work
DB_CONFIG_NOT_EXISTS = Database configuration does not exist
TEST_SQL_COLUMN_NAME_DUPLICATION_EXCEPTION = The test SQL has duplicate column names
ANALYSIS_DASHBOARD_TEMPLATE_NAME_DUPLICATION_EXCEPTION = Template name duplication
TEST_VAL_IS_NULL = Test value is null
PRODUCT_TEST_DUPLICATION_EXCEPTION = Product test duplication
THE_CACHE_IS_BEING_PROCESSED = The cache is being processed. Please try again later
MFPS_NOT_EXISTS = Process flow does not exist
MFND_NOT_EXISTS = Process node does not exist
PLAN_NOT_EXISTS = Inspection plan does not exist
CHILD_NOT_EXISTS = Sub - plan does not exist
MFPS_PLNT_INCONFORMITY = The process flow factory does not match the selected factory
PLEASE_SELECT_FACTORY = Please select a factory
STRUCTURE_UNDER_CONFIGURATION = Structure configuration is incomplete
INSPECTION_TYPE_GRP_NAME_DUPLICATION_EXCEPTION = Inspection type group name duplication
INSPECTION_TYPE_DAT_NAME_DUPLICATION_EXCEPTION = Inspection type name duplication
START_TIME_GT_END_TIME = Start date is greater than end date
PARAMETER_SET_RELEVANCE_ANALYSIS_DASHBOARD = The parameter set is already associated with an analysis page!
INSPECTION_TYPE_DAT_DUPLICATION_EXCEPTION = Some inspection types are in use
MONITOR_SAVE_NUM_EXCEPTION = The configuration of the number of monitoring cache subgroups is incorrect
MODEL_UNPUBLISHED = The process model is not published
EFFECTIVE_TOLERANCE_LIMIT = Limit not configured
UNIT_NAME_DUPLICATION_EXCEPTION = Unit name duplication
MAPPING_DATA_NOT_EXIST = Mapping data does not exist
UWL_GT_USL_EXCEPTION = Alarm limit cannot be greater than tolerance limit
URL_LT_USL_EXCEPTION = Reasonable limit cannot be less than tolerance limit
SWITCH_LANGUAGE_NOT_EXISTS = The switch language does not exist
NOT_CLOSE_PART_ALL_VERSION = It is prohibited to close all versions of the product
CHART_TYPE_MISMATCH_EXCEPTION = Chart type mismatch

# SystemExceptionEnum
LENGTH_EXCEPTION = Insufficient permissions
INPUT_STREAM_EXCEPTION = Error reading file
ACCOUNT_EXIST_EXCEPTION = Login account already exists
EMPLOYEE_CODE_EXIST_EXCEPTION = Employee code already exists
PASSWORD_NOT_NULL_EXCEPTION = Password cannot be empty
OLD_PASSWORD_NOT_SAME_EXCEPTION = Old password does not match
PASSWORD_CANNOT_SAME_HISTORY_EXCEPTION = The new password cannot be the same as the historical password
DEPT_NAME_DUPLICATION_EXCEPTION = Department name duplication
ROLE_BIND_EXCEPTION = The selected role already has bound accounts
ROLE_NAME_DUPLICATION_EXCEPTION = Role name duplication
USER_HIER_NOT_EXISTS = User hierarchy does not exist
ACCOUNT_NOT_EXIST_EXCEPTION = Account does not exist or is not activated
PERMISSION_BIND_EXCEPTION = The selected permission template already has bound roles
EMAIL_EXIST_EXCEPTION = Employee email already exists
WECHAT_EXIST_EXCEPTION = Employee enterprise WeChat already exists
DING_DING_EXIST_EXCEPTION = Employee DingTalk account already exists
MENU_ANALYSIS_PAGE_EXCEPTION = This menu has associated analysis pages
HIERARCHY_TYPE_EXCEPTION = Hierarchy type error

SUCCESS = Query successful
FAIL = Failed
​

Doubao-1.5-pro
