package com.yingfei.common.core.enums;

import com.yingfei.common.core.exception.ServiceException;
import com.yingfei.common.core.utils.I18nUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ExceptionRecordEnum {

    // 1-待审批 2-已通过 3-已撤回 4-已拒绝"
    PENDING_APPROVAL(1,"待审批"),
    ALREADY_PASSED(2,"已通过"),
    WITHDRAWN(3,"已撤回"),
    DECLINED(4,"已拒绝"),
    ;


    private final Integer type;
    private final String description;

    public static ExceptionRecordEnum find(Integer type) {
        for (ExceptionRecordEnum value : ExceptionRecordEnum.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        throw new ServiceException(I18nUtils.getMessage("INVALID_TYPE"));
    }
}
