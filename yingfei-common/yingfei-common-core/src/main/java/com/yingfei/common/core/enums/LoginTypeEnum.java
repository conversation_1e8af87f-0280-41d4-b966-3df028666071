package com.yingfei.common.core.enums;

import com.yingfei.common.core.exception.ServiceException;
import com.yingfei.common.core.utils.I18nUtils;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 登录类型。后续扩展
 */
@Getter
@RequiredArgsConstructor
public enum LoginTypeEnum {

    /**
     * PC登录
     */
    PC(1, "PC"),
    /**
     * APP登录
     */
    MOBILE(2, "Mobile");

    private final int type;
    private final String name;

    public static LoginTypeEnum getByType(int type) {
        for (LoginTypeEnum value : LoginTypeEnum.values()) {
            if (value.getType() == type) {
                return value;
            }
        }
        throw new ServiceException(I18nUtils.getMessage("INVALID_TYPE"));
    }

}
