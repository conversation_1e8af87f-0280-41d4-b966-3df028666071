package com.yingfei.common.core.i18n;

import com.yingfei.common.core.utils.I18nUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;

/**
 * I18n 自动配置类
 * 确保在所有模块中都能正确初始化国际化功能
 */
@Configuration
@ConditionalOnClass(I18nUtils.class)
public class I18nConfig {

    @Autowired(required = false)
    private MessageSource messageSource;

    /**
     * 如果没有其他 MessageSource Bean，则创建默认的
     */
    @Bean
    @ConditionalOnMissingBean(MessageSource.class)
    public ReloadableResourceBundleMessageSource defaultMessageSource() {
        ReloadableResourceBundleMessageSource messageSource = new ReloadableResourceBundleMessageSource();
        // 指定国际化资源文件路径（classpath下的i18n目录）
        messageSource.setBasename("classpath:i18n/message");
        // 设置编码格式
        messageSource.setDefaultEncoding(StandardCharsets.UTF_8.name());
        // 设置缓存时间（-1表示不缓存，开发时使用；生产环境可设置为3600等）
        messageSource.setCacheSeconds(-1);
        // 设置默认语言
        messageSource.setFallbackToSystemLocale(false);

        System.out.println("创建默认 MessageSource Bean");
        return messageSource;
    }

    @PostConstruct
    public void ensureI18nUtilsInitialized() {
        // 确保 I18nUtils 被初始化
        if (messageSource != null) {
            I18nUtils.init(messageSource);
            System.out.println("I18nUtils 通过 I18nAutoConfiguration 初始化成功");
        } else {
            System.err.println("警告: I18nAutoConfiguration 中 MessageSource 为空");
        }
    }
}
