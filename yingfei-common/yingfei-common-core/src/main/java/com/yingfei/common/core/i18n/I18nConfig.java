package com.yingfei.common.core.i18n;

import com.yingfei.common.core.utils.I18nUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;
import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;

@Configuration
public class I18nConfig {

    private MessageSource messageSource;

    @Bean(name = "customMessageSource")
    @Primary
    public ReloadableResourceBundleMessageSource messageSource() {
        ReloadableResourceBundleMessageSource messageSource = new ReloadableResourceBundleMessageSource();
        // 指定国际化资源文件路径（classpath下的i18n目录）
        messageSource.setBasename("classpath:i18n/message");
        // 设置编码格式
        messageSource.setDefaultEncoding(StandardCharsets.UTF_8.name());
        // 设置缓存时间（-1表示不缓存，开发时使用；生产环境可设置为3600等）
        messageSource.setCacheSeconds(-1);
        // 设置默认语言
        messageSource.setFallbackToSystemLocale(false);
        this.messageSource = messageSource;
        return messageSource;
    }

    @Bean
    public MessageSourceAccessor messageSourceAccessor(@Qualifier("customMessageSource") MessageSource messageSource) {
        return new MessageSourceAccessor(messageSource);
    }

    @PostConstruct
    public void initI18nUtils() {
        // 在Bean初始化完成后立即初始化I18nUtils
        if (this.messageSource != null) {
            I18nUtils.init(this.messageSource);
        }
    }
}
