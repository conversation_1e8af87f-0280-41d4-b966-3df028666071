package com.yingfei.common.core.exception.enums;

/**
 * 用户认证异常描述
 */
public enum AuthExceptionEnum implements BizExceptionEnum {

    //异常枚举
    AUTH_PERMISS_EXCEPTION(501, "权限不足"),
    AUTH_MARK_DUPLICATION_EXCEPTION(502, "权限标识重复"),
    AUTH_PASSWORD_EXCEPTION(503, "登录密码错误"),
    AUTH_ACCOUNT_EXPIRED_EXCEPTION(504, "该账户已过期"),
    AUTH_USER_NOT_LOGIN_EXCEPTION(505, "用户未登录"),
    IP_BLACKLIST(506, "该IP已被列入系统黑名单"),
    LOGIN_ACCOUNT_DOES_NOT_EXIST(507, "登录账号不存在"),
    AUTH_ACCOUNT_PASSWORD_EMPTY_EXCEPTION(508, "手机号/密码必须填写"),
    LOGIN_ACCOUNT_UP_TO_THE_LIMIT(509, "该用户登录失败次数已达上限,请1小时后重试!"),
    LOGIN_USER_COUNT_REACH_LIMIT(510, "该系统登录人数已达上限"),

    ;


    private final String domain = "auth";
    /**
     * 异常编码
     */
    private int code;

    /**
     * 异常信息
     */
    private String message;

    AuthExceptionEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }


    @Override
    public String getDomain() {
        return domain;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }

}
