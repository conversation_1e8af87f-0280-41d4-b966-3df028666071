package com.yingfei.common.core.exception;


import com.yingfei.common.core.enums.LanguageEnum;
import com.yingfei.common.core.exception.enums.BizExceptionEnum;
import com.yingfei.common.core.utils.I18nUtils;
import org.springframework.util.StringUtils;

/**
 * @Description 业务异常，所有的业务异常都必须以此类 抛出。
 */
@SuppressWarnings("serial")
public final class BusinessException extends RuntimeException {

    /**
     * 友好提示的code码
     */
    private int code;

    /**
     * 友好提示
     */
    private String message;

    /**
     * 业务域
     */
    private String domain;

    public BusinessException(int code, String message, String domain) {
        this.code = code;
        this.message = message;
        this.domain = domain;
    }

    public BusinessException(BizExceptionEnum bizExceptionEnum) {
        this.code = bizExceptionEnum.getCode();
        this.domain = bizExceptionEnum.getDomain();
        final String message = I18nUtils.getMessage(bizExceptionEnum.toString());
        if(org.apache.commons.lang3.StringUtils.isNotBlank(message)) {
            this.message = message;
        } else{
            this.message = bizExceptionEnum.getMessage();
        }
    }

    public BusinessException(BizExceptionEnum bizExceptionEnum, LanguageEnum language) {
        language = language == null ? LanguageEnum.ZH_CN : language;
        this.code = bizExceptionEnum.getCode();
        this.domain = bizExceptionEnum.getDomain();
        String msg = I18nUtils.getMessage(bizExceptionEnum.toString(),language);
        this.message = !StringUtils.hasText(msg) ? bizExceptionEnum.getMessage() : msg;
    }

    public BusinessException(BizExceptionEnum bizExceptionEnum, LanguageEnum language, Object... arg) {
        language = language == null ? LanguageEnum.ZH_CN : language;
        this.code = bizExceptionEnum.getCode();
        this.domain = bizExceptionEnum.getDomain();
        String msg = I18nUtils.getMessage(bizExceptionEnum.toString(),language);
        this.message = !StringUtils.hasText(msg) ? bizExceptionEnum.getMessage() : msg;
    }

    public BusinessException(int code, String message) {
        this.code = code;
        this.message = message;
    }


    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }
}
