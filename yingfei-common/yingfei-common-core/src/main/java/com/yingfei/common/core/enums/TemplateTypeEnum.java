//package com.yingfei.common.core.enums;
//
//import lombok.AllArgsConstructor;
//import lombok.Getter;
//
//@Getter
//@AllArgsConstructor
//public enum TemplateTypeEnum {
//
//    //
//    POST(1,"岗位模板"),
//    USER(2,"个性模版")
//    ;
//
//
//    private final Integer type;
//    private final String description;
//
////    public static TemplateTypeEnum find(Integer type) {
////        for (TemplateTypeEnum value : TemplateTypeEnum.values()) {
////            if (value.getType().equals(type)) {
////                return value;
////            }
////        }
////        return null;
////    }
//}
