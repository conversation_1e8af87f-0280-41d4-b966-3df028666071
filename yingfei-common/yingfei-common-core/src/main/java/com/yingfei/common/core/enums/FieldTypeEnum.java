package com.yingfei.common.core.enums;

import com.yingfei.common.core.exception.ServiceException;
import com.yingfei.common.core.utils.I18nUtils;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum FieldTypeEnum {

    /***
     * 字段类型。1：个人，2：通用
     */
    GENERAL(1),
    PERSONAL(2);

    private final int type;

    public static FieldTypeEnum getByType(int type) {
        for (FieldTypeEnum value : FieldTypeEnum.values()) {
            if (type == value.getType()) {
                return value;
            }
        }
        throw new ServiceException(I18nUtils.getMessage("INVALID_TYPE"));
    }
}
