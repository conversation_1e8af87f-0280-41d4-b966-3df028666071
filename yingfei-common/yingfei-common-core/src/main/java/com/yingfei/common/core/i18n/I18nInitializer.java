package com.yingfei.common.core.i18n;

import com.yingfei.common.core.utils.I18nUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.MessageSource;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * I18nUtils初始化器
 * 确保在应用启动时初始化I18nUtils
 */
@Component
@Order(1) // 设置较高优先级，确保早期初始化
public class I18nInitializer implements ApplicationRunner {

    @Autowired(required = false)
    private MessageSource messageSource;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        if (messageSource != null) {
            I18nUtils.init(messageSource);
            System.out.println("I18nUtils 初始化成功");
        } else {
            System.err.println("警告: MessageSource 未找到，I18nUtils 未能初始化");
        }
    }
}
