package com.yingfei.common.core.exception;

import com.yingfei.common.core.enums.ExceptionMessageEnum;
import com.yingfei.common.core.exception.auth.NotPermissionException;
import com.yingfei.common.core.exception.base.BaseException;
import com.yingfei.common.core.utils.I18nUtils;
import com.yingfei.common.core.utils.JudgeUtils;
import feign.FeignException;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.ReflectionException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.HashMap;
import java.util.Map;

/**
 * 全局异常统一处理
 */
@RestControllerAdvice
@Slf4j
public class ExceptionAdvice {


    @ResponseBody
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public Object exceptionHandler(MethodArgumentNotValidException ex) {
        log.warn(" threw default exception", ex);
        BaseException baseException =
                new BaseException(ex.getBindingResult().getAllErrors().get(0).getDefaultMessage());
        return this.parseJsonException(baseException);
    }

    /**
     * 拦截系统异常
     *
     * @param ex
     * @return
     */
    @ResponseBody
    @ExceptionHandler(value = Exception.class)
    public Object exceptionHandler(Exception ex) {
        log.error(" threw default exception", ex);
        final String msg = I18nUtils.getMessage(ExceptionMessageEnum.SYS_EXCEPTION.getDescription(), JudgeUtils.getLanguage());
        BaseException baseException = new BaseException(msg);
        return this.parseJsonException(baseException);
    }

    /**
     * 服务异常
     */
    @ExceptionHandler(FeignException.class)
    public Object feignException(Exception e) {
        log.error(" threw default exception", e);
        String msg = I18nUtils.getMessage(ExceptionMessageEnum.SERVICE_NOT_FOUND.getDescription(), JudgeUtils.getLanguage());
        BaseException baseException = new BaseException(msg);
        return this.parseJsonException(baseException);
    }

    @ResponseBody
    @ExceptionHandler(value = DataIntegrityViolationException.class)
    public Object dataIntegrityViolationException(DataIntegrityViolationException ex) {
        log.error(" threw default exception", ex);
        String msg = I18nUtils.getMessage(ExceptionMessageEnum.SYS_EXCEPTION.getDescription(), JudgeUtils.getLanguage());
        BaseException baseException = new BaseException(msg);
        return this.parseJsonException(baseException);
    }

    @ResponseBody
    @ExceptionHandler(value = ReflectionException.class)
    public Object reflectionExceptionHandler(Exception ex) {
        log.error(" threw default exception", ex);
        String msg = I18nUtils.getMessage(ExceptionMessageEnum.SYS_EXCEPTION.getDescription(), JudgeUtils.getLanguage());
        BaseException baseException = new BaseException(msg);
        return this.parseJsonException(baseException);
    }


    /**
     * 拦截业务异常
     *
     * @param ex
     * @return
     */
    @ResponseBody
    @ExceptionHandler(value = BaseException.class)
    public Object bizExceptionHandler(BaseException ex) {
        return this.parseJsonException(ex);
    }

    /**
     * 拦截业务异常
     *
     * @param ex
     * @return
     */
    @ResponseBody
    @ExceptionHandler(value = BusinessException.class)
    public Object businessExceptionHandler(BusinessException ex) {
        return this.parseJsonException(ex);
    }

    @ResponseBody
    @ExceptionHandler(value = NotPermissionException.class)
    public Object notPermissionException(Exception ex) {
        log.error(" threw default exception", ex);
        String msg = I18nUtils.getMessage(ExceptionMessageEnum.THE_USER_HAS_NO_PERMISSION.getDescription(), JudgeUtils.getLanguage());
        BaseException baseException = new BaseException(msg);
        return this.parseJsonException(baseException);
    }

    /**
     * 格式化输出异常信息
     *
     * @param e
     * @return
     */
    public Object parseJsonException(BaseException e) {
        Map<String, Object> exceptionMap = new HashMap<>(8);
        exceptionMap.put("code", e.getCode());
        exceptionMap.put("msg", e.getDefaultMessage());
        return exceptionMap;
    }

    /**
     * 格式化输出异常信息
     *
     * @param e
     * @return
     */
    public Object parseJsonException(BusinessException e) {
        Map<String, Object> exceptionMap = new HashMap<>(8);
        exceptionMap.put("code", e.getCode());
        exceptionMap.put("msg", e.getMessage());
        return exceptionMap;
    }
}
