package com.yingfei.dataCollection.util;

import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.exception.base.BaseException;
import com.yingfei.common.core.utils.I18nUtils;
import com.yingfei.dataCollection.service.impl.ScheduleJobUtils;
import com.yingfei.entity.domain.SCHEDULE_JOB_INF;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;

import java.util.Objects;
@Slf4j
public class ScheduleUtils {

    private final static String JOB_NAME = "TASK_";

    /**
     * 获取触发器key
     */
    public static TriggerKey getTriggerKey(Long jobId) {
        return TriggerKey.triggerKey(JOB_NAME + jobId);
    }

    /**
     * 获取jobKey
     */
    public static JobKey getJobKey(Long jobId) {
        return JobKey.jobKey(JOB_NAME + jobId);
    }

    /**
     * 获取表达式触发器
     */
    public static CronTrigger getCronTrigger(Scheduler scheduler, Long jobId) {
        try {
            return (CronTrigger) scheduler.getTrigger(getTriggerKey(jobId));
        } catch (SchedulerException e) {
            new BaseException(I18nUtils.getMessage("FAILED_TO_GET_EXPRESSION_TRIGGER"));
        }
        return null;
    }

    /**
     * 创建定时任务
     */
    public static void createScheduleJob(Scheduler scheduler, SCHEDULE_JOB_INF scheduleJob) {
        try {
            //构建job信息
            JobDetail jobDetail = JobBuilder.newJob(ScheduleJobUtils.class).withIdentity(getJobKey(scheduleJob.getF_SJOB())).build();

            //表达式调度构建器
            CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(scheduleJob.getF_CRON())
                    .withMisfireHandlingInstructionDoNothing();

            //按新的cronExpression表达式构建一个新的trigger
            CronTrigger trigger = TriggerBuilder.newTrigger().withIdentity(getTriggerKey(scheduleJob.getF_SJOB())).withSchedule(scheduleBuilder).build();

            //放入参数，运行时的方法可以获取
            jobDetail.getJobDataMap().put(Constants.JOB_PARAM_KEY, scheduleJob);

            scheduler.scheduleJob(jobDetail, trigger);

            //暂停任务
            if (Objects.equals(scheduleJob.getF_STATUS(), Constants.PAUSE)) {
                pauseJob(scheduler, scheduleJob.getF_SJOB());
            }
        } catch (SchedulerException e) {
            throw new BaseException(I18nUtils.getMessage("FAILED_TO_CREATE_SCHEDULED_TASK"));
        }
    }

    /**
     * 更新定时任务
     */
    public static void updateScheduleJob(Scheduler scheduler, SCHEDULE_JOB_INF scheduleJob) {
        try {
            TriggerKey triggerKey = getTriggerKey(scheduleJob.getF_SJOB());

            //表达式调度构建器
            CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(scheduleJob.getF_CRON())
                    .withMisfireHandlingInstructionDoNothing();

            CronTrigger trigger = getCronTrigger(scheduler, scheduleJob.getF_SJOB());

            //按新的cronExpression表达式重新构建trigger
            assert trigger != null;
            trigger = trigger.getTriggerBuilder().withIdentity(triggerKey).withSchedule(scheduleBuilder).build();

            //参数
            trigger.getJobDataMap().put(Constants.JOB_PARAM_KEY, scheduleJob);

            scheduler.rescheduleJob(triggerKey, trigger);

            //暂停任务
            if (Objects.equals(scheduleJob.getF_STATUS(), Constants.PAUSE)) {
                pauseJob(scheduler, scheduleJob.getF_SJOB());
            }

        } catch (SchedulerException e) {
            throw new BaseException(I18nUtils.getMessage("FAILED_TO_UPDATE_SCHEDULED_TASK"));
        }
    }

    /**
     * 根据任务信息查询定时任务是否存在
     * @param scheduler 调度器实例
     * @param fsjob 任务信息ID
     * @return 任务存在返回true，否则返回false
     */
    public static boolean checkJobExists(Scheduler scheduler, Long fsjob) {
        try {
            // 使用与创建任务相同的方式生成JobKey
            final JobKey jobKey = getJobKey(fsjob);
            // 检查任务是否存在
            return scheduler.checkExists(jobKey);
        } catch (SchedulerException e) {
            // 记录异常日志
            log.error("检查任务存在性失败, 任务名称: {}", fsjob, e);
            // 异常情况下返回false或根据业务需求处理
            return false;
        }
    }


    /**
     * 立即执行任务
     */
    public static void run(Scheduler scheduler, SCHEDULE_JOB_INF scheduleJob) {
        try {
            //参数
            JobDataMap dataMap = new JobDataMap();
            dataMap.put(Constants.JOB_PARAM_KEY, scheduleJob);

            scheduler.triggerJob(getJobKey(scheduleJob.getF_SJOB()), dataMap);
        } catch (SchedulerException e) {
            throw new BaseException(I18nUtils.getMessage("FAILED_TO_EXECUTE_SCHEDULED_TASK_IMMEDIATELY"));
        }
    }

    /**
     * 暂停任务
     */
    public static void pauseJob(Scheduler scheduler, Long jobId) {
        try {
            scheduler.pauseJob(getJobKey(jobId));
        } catch (SchedulerException e) {
            throw new BaseException(I18nUtils.getMessage("FAILED_TO_PAUSE_SCHEDULED_TASK"));
        }
    }

    /**
     * 恢复任务
     */
    public static void resumeJob(Scheduler scheduler, Long jobId) {
        try {
            scheduler.resumeJob(getJobKey(jobId));
        } catch (SchedulerException e) {
            throw new BaseException(I18nUtils.getMessage("FAILED_TO_RESUME_SCHEDULED_TASK"));
        }
    }

    /**
     * 删除定时任务
     */
    public static void deleteScheduleJob(Scheduler scheduler, Long jobId) {
        try {
            scheduler.deleteJob(getJobKey(jobId));
        } catch (SchedulerException e) {
            throw new BaseException(I18nUtils.getMessage("FAILED_TO_DELETE_SCHEDULED_TASK"));
        }
    }

}

