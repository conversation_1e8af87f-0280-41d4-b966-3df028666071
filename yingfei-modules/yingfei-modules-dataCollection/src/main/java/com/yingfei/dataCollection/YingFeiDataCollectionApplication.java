package com.yingfei.dataCollection;

import com.yingfei.common.security.annotation.EnableCustomConfig;
import com.yingfei.common.security.annotation.EnableRyFeignClients;
import com.yingfei.common.swagger.annotation.EnableCustomSwagger2;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.TimeZone;


/**
 * 数据采集模块
 */
@EnableCustomConfig
@EnableCustomSwagger2
@EnableRyFeignClients
@SpringBootApplication
@EnableScheduling
@ComponentScan(basePackages = {"com.yingfei"})
public class YingFeiDataCollectionApplication {
    public static void main(String[] args) throws Exception {
//        TimeZone.setDefault(TimeZone.getTimeZone("UTC"));
        SpringApplication.run(YingFeiDataCollectionApplication.class, args);
        System.out.println("数据采集服务启动成功");

    }
}
