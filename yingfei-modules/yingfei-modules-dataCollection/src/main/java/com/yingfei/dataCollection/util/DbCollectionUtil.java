package com.yingfei.dataCollection.util;

import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.exception.base.BaseException;
import com.yingfei.common.core.utils.I18nUtils;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.entity.dto.dataImport.DataColumnMappingDTO;
import com.yingfei.entity.enums.EvaluationTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.jexl3.JexlContext;
import org.apache.commons.jexl3.JexlEngine;
import org.apache.commons.jexl3.JexlScript;
import org.apache.commons.jexl3.MapContext;
import org.apache.commons.jexl3.internal.Engine;

import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class DbCollectionUtil {

    public static Boolean getBoolean(String cacheKey, Integer type, Map<String, String> saveMap, String itemName, Double saveValue) {
        boolean isSave = false;
        try {
            if (saveMap.get(cacheKey) == null) {
                saveMap.put(cacheKey, itemName);
            } else {
                switch (EvaluationTypeEnum.getType(type)) {
                    case NEW:
                        /*判断是否和前一个值不同*/
                        if (!itemName.equals(saveMap.get(cacheKey))) {
                            isSave = true;
                            saveMap.put(cacheKey, itemName);
                        }
                        break;
                    case LESS:
                        if (Double.parseDouble(saveMap.get(cacheKey)) < saveValue) {
                            isSave = true;
                            saveMap.put(cacheKey, itemName);
                        }
                        break;
                    case LESS_OR_EQUAL:
                        if (Double.parseDouble(saveMap.get(cacheKey)) <= saveValue) {
                            isSave = true;
                            saveMap.put(cacheKey, itemName);
                        }
                        break;
                    case EQUAL:
                        if (Double.parseDouble(saveMap.get(cacheKey)) == saveValue) {
                            isSave = true;
                            saveMap.put(cacheKey, itemName);
                        }
                        break;
                    case NOT_EQUAL:
                        if (Double.parseDouble(saveMap.get(cacheKey)) != saveValue) {
                            isSave = true;
                            saveMap.put(cacheKey, itemName);
                        }
                        break;
                    case GREATER_OR_EQUAL:
                        if (Double.parseDouble(saveMap.get(cacheKey)) >= saveValue) {
                            isSave = true;
                            saveMap.put(cacheKey, itemName);
                        }
                        break;
                    case GREATER:
                        if (Double.parseDouble(saveMap.get(cacheKey)) > saveValue) {
                            isSave = true;
                            saveMap.put(cacheKey, itemName);
                        }
                        break;
                    case NULL:
                        if (StringUtils.isEmpty(saveMap.get(cacheKey))) {
                            isSave = true;
                            saveMap.put(cacheKey, itemName);
                        }
                        break;
                }
            }
        } catch (Exception e) {
            log.error("保存条件解析错误-------->{}", itemName);
            throw new BaseException(I18nUtils.getMessage("SAVE_CONDITION_PARSING_ERROR"));
        }
        return isSave;
    }

    private static final Pattern pattern = Pattern.compile("\\{[^}]*\\}");

    /**
     * 获取项的值
     *
     * @param type     1.静态字段 2.字段拼接
     * @param name     待处理内容
     * @param filedMap
     * @return
     */
    public static String getItemName(Integer type, String name, Map<String, Object> filedMap) {
        if (type == 1) {
            return name;
        } else {
            if (StringUtils.isEmpty(name)) return "";
            /*正则取值*/
            Matcher matcher = pattern.matcher(name);
            StringBuilder resultString = new StringBuilder();
            while (matcher.find()) {
                String variable = matcher.group(0);
                variable = variable.replace("{", "");
                variable = variable.replace("}", "");
                String replacement = "";
                if (name.contains("value")) {
                    replacement = filedMap.getOrDefault(variable, matcher.group(0)).toString();
                } else {
                    /*reading*/
                    replacement = "1";
                }
                matcher.appendReplacement(resultString, replacement);
            }
            matcher.appendTail(resultString);
            return resultString.toString().split(Constants.COMMA)[1].replace(")", "");
        }
    }

    /**
     * 测试值解析
     *
     * @param type     1.静态字段 2.字段拼接
     * @param name     待处理内容
     * @param filedMap
     * @return
     */
    public static Double getTestValue(Integer type, String name, Map<String, Object> filedMap) {
        if (type == 1) {
            return Double.valueOf(name);
        } else {
            /*正则取值*/
            Matcher matcher = pattern.matcher(name);
            StringBuilder resultString = new StringBuilder();

            while (matcher.find()) {
                String variable = matcher.group(0);
                variable = variable.replace("{", "");
                variable = variable.replace("}", "");
                String replacement = "";
                if (name.contains("value")) {
                    replacement = filedMap.getOrDefault(variable, matcher.group(0)).toString();
                } else {
                    /*reading*/
                    replacement = "1";
                }
                matcher.appendReplacement(resultString, replacement);
            }
            matcher.appendTail(resultString);
            if (StringUtils.isEmpty(resultString.toString())) return Double.NaN;
            /*自动计算*/
            try {
                JexlEngine engine = new Engine();
                JexlContext context = new MapContext();
                JexlScript script = engine.createScript(resultString.toString().split(Constants.COMMA)[1].replace(")", ""));
                Object execute = script.execute(context);
                return Double.valueOf(execute.toString());
            } catch (Exception e) {
                log.error("测试值解析错误------>{}", resultString.toString());
                return Double.NaN;
            }
        }
    }

    public static String getItemName(Integer type, List<DataColumnMappingDTO.AnalyticRule> analyticRuleList, String name, Map<String, Object> map) {
        String value = "";
        if (type == 1) {
            StringBuilder sb = new StringBuilder();
            for (DataColumnMappingDTO.AnalyticRule analyticRule : analyticRuleList) {
                if (StringUtils.isNotEmpty(analyticRule.getConnector())) {
                    sb.append(analyticRule.getConnector());
                }
                sb.append(map.get(analyticRule.getName()));
            }
            value = sb.toString();
        } else {
            value = name;
        }
        return value;
    }
}
