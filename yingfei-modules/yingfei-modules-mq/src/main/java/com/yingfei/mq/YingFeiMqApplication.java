package com.yingfei.mq;

import com.yingfei.common.security.annotation.EnableCustomConfig;
import com.yingfei.common.security.annotation.EnableRyFeignClients;
import com.yingfei.common.swagger.annotation.EnableCustomSwagger2;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.TimeZone;


/**
 * 消息中间件模块
 */
@EnableCustomConfig
@EnableCustomSwagger2
@EnableRyFeignClients
@SpringBootApplication
@EnableScheduling
@ComponentScan(basePackages = {"com.yingfei"})
public class YingFeiMqApplication {
    public static void main(String[] args) throws Exception {
        TimeZone.setDefault(TimeZone.getTimeZone("UTC"));
        SpringApplication.run(YingFeiMqApplication.class, args);
        System.out.println("消息中间件服务启动成功");

    }
}
