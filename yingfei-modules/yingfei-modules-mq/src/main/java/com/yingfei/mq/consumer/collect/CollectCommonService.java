package com.yingfei.mq.consumer.collect;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.rabbitmq.client.AMQP;
import com.rabbitmq.client.Channel;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.enums.DelFlagEnum;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.DateUtils;
import com.yingfei.common.core.utils.JudgeUtils;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.redis.configure.RedisConstant;
import com.yingfei.common.redis.service.RedisService;
import com.yingfei.dataManagement.api.RemoteScheduleJobService;
import com.yingfei.entity.domain.*;
import com.yingfei.entity.dto.SGRP_VAL_CHILD_DTO;
import com.yingfei.entity.dto.autoCollect.AcquisitionTaskStatisticsDTO;
import com.yingfei.entity.dto.dataImport.FileNameDataDTO;
import com.yingfei.entity.vo.SubgroupDataVO;
import com.yingfei.mq.config.RabbitConfig;
import com.yingfei.mq.mapper.INSPECTION_PLAN_RECORD_INFMapper;
import com.yingfei.mq.mapper.SCHEDULE_JOB_INFMapper;
import com.yingfei.mq.mapper.SGRP_INF_UNFINISHEDMapper;
import com.yingfei.mq.mapper.TEST_INFMapper;
import com.yingfei.mq.producer.SubgroupDataCacheProducer;
import com.yingfei.mq.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CollectCommonService {

    @Resource
    private SubgroupService sgrpInfService;
    @Resource
    private SGRP_VALService sgrpValService;
    @Resource
    private SGRP_DSCService sgrpDscService;
    @Resource
    private SGRP_CMTService sgrpCmtService;
    @Resource
    private MESSAGE_LOG_INFService messageLogInfService;
    @Resource
    private SubgroupDataCacheProducer subgroupDataCacheProducer;
    @Resource
    private INSPECTION_PLAN_RECORD_INFMapper inspectionPlanRecordInfMapper;
    @Resource
    private SCHEDULE_JOB_INFMapper scheduleJobInfMapper;
    @Resource
    private RemoteScheduleJobService remoteScheduleJobService;
    @Resource
    private RedisService redisService;
    @Resource
    private SGRP_INF_UNFINISHEDMapper sgrpInfUnfinishedMapper;
    @Resource
    private TEST_INFMapper testInfMapper;

    public void extracted(String queueMessage, long deliveryTag, CorrelationData correlationData, Channel channel) throws IOException {
        MESSAGE_LOG_INF messageLogInf = messageLogInfService.getById(Long.valueOf(correlationData.getId()));
        if (messageLogInf == null) {
            messageLogInf = redisService.getCacheObject(RedisConstant.MESSAGE_LOG + correlationData.getId());
            messageLogInfService.save(messageLogInf);
        }
        List<SubgroupDataVO> subgroupDataVOList = null;
        FileNameDataDTO fileNameDataDTO = null;
        AcquisitionTaskStatisticsDTO acquisitionTaskStatisticsDTO = null;
        try {
            long start = System.currentTimeMillis();
            if (messageLogInf.getF_TYPE() == 3) {
                fileNameDataDTO = JSONObject.parseObject(queueMessage, FileNameDataDTO.class);
                Integer cacheObject = redisService.getCacheObject(RedisConstant.CLEAR_MQ + fileNameDataDTO.getJobId());
                if (cacheObject != null && cacheObject == 2) {
                    log.info("需要清空MQ消息,不执行后续逻辑");
                    channel.queuePurge(RabbitConfig.NORMAL_QUEUE_NAME);
                    channel.basicAck(deliveryTag, false);
                    redisService.deleteObject(RedisConstant.CLEAR_MQ + fileNameDataDTO.getJobId());
                    return;
                }
                subgroupDataVOList = redisService.getCacheMapValue(String.format(RedisConstant.BE_DEALING_WITH, fileNameDataDTO.getJobId()), fileNameDataDTO.getName());
                /*获取采集任务状态统计*/
                acquisitionTaskStatisticsDTO = redisService.getCacheObject(String.format(RedisConstant.ACQUISITION_TASK_STATISTICS, fileNameDataDTO.getJobId()));
                if (acquisitionTaskStatisticsDTO == null) {
                    acquisitionTaskStatisticsDTO = new AcquisitionTaskStatisticsDTO();
                    acquisitionTaskStatisticsDTO.setJobId(fileNameDataDTO.getJobId());
                }
                /*记录已处理数量*/
                acquisitionTaskStatisticsDTO.setProcessedNum(acquisitionTaskStatisticsDTO.getProcessedNum() + 1);
            } else {
                subgroupDataVOList = JSONArray.parseArray(queueMessage, SubgroupDataVO.class);
            }
            if (CollectionUtils.isEmpty(subgroupDataVOList)) {
                log.info("子组采集消息为空");
                channel.basicAck(deliveryTag, false);
                return;
            }
            Long plan = subgroupDataVOList.get(0).getF_INSP_PLAN();
            /*获取检查计划对应的定时任务*/
            LambdaQueryWrapper<SCHEDULE_JOB_INF> jobQueryWrapper = new LambdaQueryWrapper<>();
            jobQueryWrapper.eq(SCHEDULE_JOB_INF::getF_BUID, plan)
                    .eq(SCHEDULE_JOB_INF::getF_STATUS, DelFlagEnum.USE.getType());
            List<SCHEDULE_JOB_INF> scheduleJobInfList = scheduleJobInfMapper.selectList(jobQueryWrapper);
            if (CollectionUtils.isNotEmpty(scheduleJobInfList)) {
                /*暂停任务*/
                remoteScheduleJobService.pause(scheduleJobInfList.stream().map(SCHEDULE_JOB_INF::getF_SJOB).collect(Collectors.toList()));
            }


            List<SGRP_INF> sgrpInfList = new ArrayList<>();
            MESSAGE_LOG_INF finalMessageLogInf = messageLogInf;
            //查询测试类型
            Set<Long> testIdSet = new HashSet<>();
            subgroupDataVOList.stream().forEach(subgroupDataVO -> {
                for (SGRP_VAL_CHILD_DTO sgrpValChildDto : subgroupDataVO.getSgrpValChildDtoList()) {
                    testIdSet.add(sgrpValChildDto.getTestId());
                }
            });
            Map<Long, Integer> testTypeMap = new HashMap<>();
             if(!ObjectUtils.isEmpty(testIdSet)) {
                 LambdaQueryWrapper<TEST_INF> query = new LambdaQueryWrapper<>();
                 query.in(TEST_INF::getF_TEST, testIdSet)
                         .eq(TEST_INF::getF_DEL, DelFlagEnum.USE.getType());
                 List<TEST_INF> testDataList = testInfMapper.selectList(query);
                 if(CollectionUtils.isNotEmpty(testDataList)) {
                     testTypeMap = testDataList.stream()
                             .collect(Collectors.toMap(
                                     TEST_INF::getF_TEST,
                                     TEST_INF::getF_TYPE,
                                     (v1, v2) -> v1
                             ));
                 }
             }
            Map<Long, Integer> finalTestTypeMap = testTypeMap;
            subgroupDataVOList.forEach(subgroupDataVO -> {
                subgroupDataVO.setF_SGRP(JudgeUtils.defaultIdentifierGenerator.nextId(null));
                if (subgroupDataVO.getF_SGTM() == null) subgroupDataVO.setF_SGTM(DateUtils.getNowDate());
                if (subgroupDataVO.getF_CRUE() == null)
                    subgroupDataVO.setF_CRUE(finalMessageLogInf.getF_CRUE());
                if (StringUtils.isEmpty(subgroupDataVO.getF_EDUE()))
                    subgroupDataVO.setF_EDUE(finalMessageLogInf.getF_CRUE());
                /*处理子组数据*/
                SGRP_INF sgrpInf = new SGRP_INF();
                BeanUtils.copyPropertiesIgnoreNull(subgroupDataVO, sgrpInf);
                Set<Long> collect =
                        subgroupDataVO.getSgrpValChildDtoList().stream().map(SGRP_VAL_CHILD_DTO::getChildId).collect(Collectors.toSet());
                sgrpInf.setF_NUM(collect.size());
                sgrpInfList.add(sgrpInf);
                //添加测试类型
                subgroupDataVO.getSgrpValChildDtoList().forEach(sgrpValChildDto -> {
                    if (!ObjectUtils.isEmpty(finalTestTypeMap) && !ObjectUtils.isEmpty(sgrpValChildDto.getTestId())) {
                        sgrpValChildDto.setTestType(finalTestTypeMap.get(sgrpValChildDto.getTestId()));
                    }
                });
            });
            sgrpInfService.saveBatch(sgrpInfList);

            /*判断子计划是否已完成*/
            if (subgroupDataVOList.get(0).getF_FINISH_STATUS() == YesOrNoEnum.YES.getType() && messageLogInf.getF_TYPE() != 3) {
                /*查询唯一标识的子组*/
                LambdaQueryWrapper<SGRP_INF> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(SGRP_INF::getF_SAMPLE_ID, subgroupDataVOList.get(0).getF_SAMPLE_ID())
                        .eq(SGRP_INF::getF_FINISH_STATUS, YesOrNoEnum.NO.getType())
                        .eq(SGRP_INF::getF_DEL, DelFlagEnum.USE.getType());
                List<SGRP_INF> list = sgrpInfService.list(queryWrapper);
                if (CollectionUtils.isNotEmpty(list)) {
                    LambdaUpdateWrapper<SGRP_INF> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.eq(SGRP_INF::getF_SAMPLE_ID, subgroupDataVOList.get(0).getF_SAMPLE_ID())
                            .eq(SGRP_INF::getF_FINISH_STATUS, YesOrNoEnum.NO.getType())
                            .eq(SGRP_INF::getF_DEL, DelFlagEnum.USE.getType())
                            .set(SGRP_INF::getF_FINISH_STATUS, YesOrNoEnum.YES.getType());
                    sgrpInfService.update(null, updateWrapper);

                    /*删除对应待处理子组表数据*/
                    LambdaQueryWrapper<SGRP_INF_UNFINISHED> wrapper = new LambdaQueryWrapper<>();
                    wrapper.in(SGRP_INF_UNFINISHED::getF_SAMPLE_ID, list.stream().map(SGRP_INF::getF_SAMPLE_ID).collect(Collectors.toList()));
                    sgrpInfUnfinishedMapper.delete(wrapper);
                }
            }

            /*处理业务逻辑*/
            List<SGRP_VAL> sgrpValList = new ArrayList<>();
            List<SGRP_DSC> sgrpDscList = new ArrayList<>();
            List<SGRP_CMT> sgrpCmtList = new ArrayList<>();
            subgroupDataVOList.forEach(subgroupDataVO -> {
                /*保存测试对象*/
                sgrpValList.addAll(saveTest(subgroupDataVO, subgroupDataVO.getSgrpValChildDtoList()));
                /*保存子组描述符对象*/
                sgrpDscList.addAll(saveDsc(subgroupDataVO.getF_SGRP(), subgroupDataVO.getSgrpDscList()));
                /*保存子组备注对象*/
                sgrpCmtList.addAll(saveCmt(subgroupDataVO, subgroupDataVO.getSgrpCmtList()));
            });

            sgrpValService.saveBatch(sgrpValList);
            sgrpDscService.saveBatch(sgrpDscList);
            sgrpCmtService.saveBatch(sgrpCmtList);

            messageLogInf.setF_STATUS(3);
            channel.basicAck(deliveryTag, false);
            messageLogInfService.updateById(messageLogInf);
            long end = System.currentTimeMillis();
            log.info("数据库新增子组耗时,子组数量---------->{},{}", end - start, subgroupDataVOList.size());

            if (acquisitionTaskStatisticsDTO != null) {
                AMQP.Queue.DeclareOk declareOk = channel.queueDeclarePassive(RabbitConfig.NORMAL_QUEUE_NAME);
                /*获取MQ队列剩余数量*/
                int messageCount = declareOk.getMessageCount();
                acquisitionTaskStatisticsDTO.setUntreatedNum(messageCount);
                Integer successfulNum = acquisitionTaskStatisticsDTO.getSuccessNum();
                acquisitionTaskStatisticsDTO.setSuccessNum(successfulNum + 1);
                /*获取平均保存时间 = ((平均时间*初始成功数量)+当前耗时)/当前成功数量 */
                long l = ((acquisitionTaskStatisticsDTO.getMeanStorageTime() * successfulNum) + (end - start)) / acquisitionTaskStatisticsDTO.getSuccessNum();
                acquisitionTaskStatisticsDTO.setMeanStorageTime(l);
                acquisitionTaskStatisticsDTO.setLastSaveTime(DateUtils.dateTimeTwo(DateUtils.getNowDate()));
            }

            try {
                /*存入缓存表_A*/
                subgroupDataCacheProducer.send(subgroupDataVOList, sgrpInfList, sgrpValList, sgrpDscList, sgrpCmtList);
            } catch (Exception e) {
                log.error("发送子组缓存信息失败------->", e);
                e.printStackTrace();
            }

            /*修改检查计划定时执行状态*/
            LambdaQueryWrapper<INSPECTION_PLAN_RECORD_INF> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(INSPECTION_PLAN_RECORD_INF::getF_PLAN, plan)
                    .eq(INSPECTION_PLAN_RECORD_INF::getF_TYPE, DelFlagEnum.USE.getType());
            List<INSPECTION_PLAN_RECORD_INF> inspectionPlanRecordInfList = inspectionPlanRecordInfMapper.selectList(queryWrapper);
            if (CollectionUtils.isNotEmpty(inspectionPlanRecordInfList)) {
                INSPECTION_PLAN_RECORD_INF inspectionPlanRecordInf = INSPECTION_PLAN_RECORD_INF.init();
                inspectionPlanRecordInf.setF_TYPE(1);
                inspectionPlanRecordInfMapper.update(inspectionPlanRecordInf, queryWrapper);
            } else {
                /*提前执行添加执行记录*/
                INSPECTION_PLAN_RECORD_INF inspectionPlanRecordInf = new INSPECTION_PLAN_RECORD_INF();
                inspectionPlanRecordInf.setF_PLAN(plan);
                List<SCHEDULE_JOB_INF> scheduleJobInfs = scheduleJobInfList.stream().filter(s -> s.getF_TASK_IDENTIFY().equals(Constants.checkPlanIdentify)).collect(Collectors.toList());
                inspectionPlanRecordInf.setF_SJOB(CollectionUtils.isEmpty(scheduleJobInfs) ? 0L : scheduleJobInfs.get(0).getF_SJOB());
                inspectionPlanRecordInf.setF_CRUE(subgroupDataVOList.get(0).getF_CRUE());
                inspectionPlanRecordInf.setF_EDUE(subgroupDataVOList.get(0).getF_EDUE());
                inspectionPlanRecordInf.setF_CRTM(DateUtils.getNowDate());
                inspectionPlanRecordInf.setF_EDTM(DateUtils.getNowDate());
                inspectionPlanRecordInfMapper.insert(inspectionPlanRecordInf);
            }
            if (CollectionUtils.isNotEmpty(scheduleJobInfList)) {
                /*恢复任务*/
                remoteScheduleJobService.resume(scheduleJobInfList.stream().map(SCHEDULE_JOB_INF::getF_SJOB).collect(Collectors.toList()));
            }

            /*判断是否有子组缓存数据*/
            subgroupDataVOList.forEach(subgroupDataVO -> {
                String key = String.format(RedisConstant.SUBGROUP_INPUT_TEMP, subgroupDataVO.getF_INSP_PLAN(), subgroupDataVO.getF_CRUE());
                Map<String, List<SubgroupDataVO>> cacheMap = redisService.getCacheMap(key);
                if (MapUtils.isNotEmpty(cacheMap)) {
                    if (cacheMap.get(subgroupDataVO.getF_SAMPLE_ID()) != null) {
                        cacheMap.remove(subgroupDataVO.getF_SAMPLE_ID());
                    }
                    redisService.deleteObject(key);
                    if (!cacheMap.isEmpty()) {
                        redisService.setCacheMap(key, cacheMap);
                    }
                }

                /*删除正在处理缓存*/
                String processingKey = String.format(RedisConstant.SUBGROUP_PROCESSING_TEMP, subgroupDataVO.getF_INSP_PLAN());
                List<String> cacheList = redisService.getCacheList(processingKey);
                if (CollectionUtils.isNotEmpty(cacheList)) {
                    cacheList.remove(subgroupDataVO.getF_SAMPLE_ID());
                    redisService.deleteObject(processingKey);
                    if (CollectionUtils.isNotEmpty(cacheList)) {
                        redisService.setCacheList(processingKey, cacheList, Constants.REDIS_EXPIRE_TIME);
                    }
                }

                String pendingKey = String.format(RedisConstant.SUBGROUP_PROCESSING_PENDING, subgroupDataVO.getF_INSP_PLAN());
                List<String> pendingCacheList = redisService.getCacheList(pendingKey);
                if (CollectionUtils.isNotEmpty(pendingCacheList)) {
                    cacheList.remove(subgroupDataVO.getSgrpValChildDtoList().get(0).getChildId());
                    redisService.deleteObject(pendingKey);
                    if (CollectionUtils.isNotEmpty(pendingCacheList)) {
                        redisService.setCacheList(pendingKey, pendingCacheList, Constants.REDIS_EXPIRE_TIME);
                    }
                }
            });

        } catch (Exception e) {
            e.printStackTrace();
            log.error("子组采集消息处理失败,错误信息---------->", e);
            if (messageLogInf.getF_TYPE() == 3 && fileNameDataDTO != null && fileNameDataDTO.getType() == 1) {
                /*将正在处理缓存,文件名数据缓存删除并变为处理失败缓存*/
                redisService.setCacheMapValue(String.format(RedisConstant.PROCESSING_FAILURE_CACHE, fileNameDataDTO.getJobId()), fileNameDataDTO.getName(), subgroupDataVOList, null);
                channel.basicAck(deliveryTag, false);
            } else {
                channel.basicNack(deliveryTag, false, false);
            }
            if (acquisitionTaskStatisticsDTO != null) {
                /*记录失败数据*/
                List<SubgroupDataVO> loseData = acquisitionTaskStatisticsDTO.getFailData();
                loseData.addAll(subgroupDataVOList);
                acquisitionTaskStatisticsDTO.setFailData(loseData);
                acquisitionTaskStatisticsDTO.setFailNum(acquisitionTaskStatisticsDTO.getFailNum() + 1);
                /*记录失败原因*/
                acquisitionTaskStatisticsDTO.setLastFailReason(e.getMessage());
            }

            messageLogInf.setF_STATUS(4);
            messageLogInfService.updateById(messageLogInf);
            /*todo 发送检查计划失败通知*/

        } finally {
            /*删除数据采集正在处理和待处理的缓存*/
            if (messageLogInf.getF_TYPE() == 3 && fileNameDataDTO != null) {
                redisService.deleteCacheMapValue(String.format(RedisConstant.BE_DEALING_WITH, fileNameDataDTO.getJobId()), fileNameDataDTO.getName());
                /*删除待处理缓存*/
                redisService.deleteCacheMapValue(String.format(RedisConstant.FILENAME_DATA_CACHE, fileNameDataDTO.getJobId()), fileNameDataDTO.getName());

                /*保存采集任务状态统计*/
                if (acquisitionTaskStatisticsDTO != null) {
                    redisService.setCacheObject(String.format(RedisConstant.ACQUISITION_TASK_STATISTICS, fileNameDataDTO.getJobId()), acquisitionTaskStatisticsDTO);
                }
            }

        }
    }


    /**
     * 保存测试对象
     */
    private List<SGRP_VAL> saveTest(SubgroupDataVO subgroupDataVO, List<SGRP_VAL_CHILD_DTO> sgrpValChildDtoList) {
        List<SGRP_VAL> list = SGRP_VAL_CHILD_DTO.computeVal(subgroupDataVO, sgrpValChildDtoList);
//        sgrpValService.saveBatch(list);
        return list;
    }

    /**
     * 保存子组描述符
     *
     * @param fSgrp
     * @param sgrpDscList
     */
    private List<SGRP_DSC> saveDsc(Long fSgrp, List<SGRP_DSC> sgrpDscList) {
        if (CollectionUtils.isEmpty(sgrpDscList)) return new ArrayList<>();
        sgrpDscList.forEach(sgrpDsc -> {
            sgrpDsc.setF_SGRP(fSgrp);
        });
//        sgrpDscService.saveBatch(sgrpDscList);
        return sgrpDscList;
    }

    /**
     * 保存子组备注
     *
     * @param subgroupDataVO
     * @param sgrpCmtList
     */
    private List<SGRP_CMT> saveCmt(SubgroupDataVO subgroupDataVO, List<SGRP_CMT> sgrpCmtList) {
        if (CollectionUtils.isEmpty(sgrpCmtList)) return new ArrayList<>();
        sgrpCmtList.forEach(sgrpCmt -> {
            sgrpCmt.setF_SGRP(subgroupDataVO.getF_SGRP());
            sgrpCmt.setF_CRUE(subgroupDataVO.getF_CRUE());
            sgrpCmt.setF_EDUE(subgroupDataVO.getF_EDUE());
        });
//        sgrpCmtService.saveBatch(sgrpCmtList);
        return sgrpCmtList;
    }
}
