package com.yingfei.dataManagement;

import com.yingfei.common.security.annotation.EnableCustomConfig;
import com.yingfei.common.security.annotation.EnableRyFeignClients;
import com.yingfei.common.swagger.annotation.EnableCustomSwagger2;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.TimeZone;


/**
 * 数据管理,基础信息,数据查询模块
 */
@ServletComponentScan
@EnableCustomConfig
@EnableCustomSwagger2
@EnableRyFeignClients
@SpringBootApplication
@EnableScheduling
@ComponentScan(basePackages = {"com.yingfei"})
public class YingFeiDataManageApplication {
    public static void main(String[] args) throws Exception {
        TimeZone.setDefault(TimeZone.getTimeZone("UTC"));
        SpringApplication.run(YingFeiDataManageApplication.class, args);
        System.out.println("数据管理服务启动成功");
    }
}
