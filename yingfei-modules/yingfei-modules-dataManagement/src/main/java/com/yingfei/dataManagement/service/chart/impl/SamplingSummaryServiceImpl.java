package com.yingfei.dataManagement.service.chart.impl;

import com.alibaba.fastjson2.JSONObject;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.exception.base.BaseException;
import com.yingfei.common.core.utils.I18nUtils;
import com.yingfei.common.redis.service.RedisService;
import com.yingfei.dataManagement.mapper.*;
import com.yingfei.dataManagement.service.SPEC_INFService;
import com.yingfei.dataManagement.service.chart.ANALYSIS_DASHBOARD_TEMPLATE_INFService;
import com.yingfei.dataManagement.service.chart.BoxPlotsService;
import com.yingfei.dataManagement.service.chart.ChartCommonService;
import com.yingfei.dataManagement.service.chart.SamplingSummaryService;
import com.yingfei.entity.domain.*;
import com.yingfei.entity.dto.DataSummaryDTO;
import com.yingfei.entity.dto.SPEC_INF_DTO;
import com.yingfei.entity.dto.SubgroupDataDTO;
import com.yingfei.entity.dto.SubgroupDataSelectionDTO;
import com.yingfei.entity.dto.chart.*;
import com.yingfei.entity.enums.ChartTypeEnum;
import com.yingfei.entity.enums.SamplingSummaryAnalyseTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.Collator;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SamplingSummaryServiceImpl implements SamplingSummaryService {

    @Resource
    private RedisService redisService;
    @Resource
    private SPEC_INFService specInfService;
    @Resource
    private ANALYSIS_DASHBOARD_TEMPLATE_INFService analysisDashboardTemplateInfService;
    @Resource
    private BoxPlotsService boxPlotsService;
    @Resource
    private SHIFT_DATMapper shiftDatMapper;
    @Resource
    private TEST_INFMapper testInfMapper;
    @Resource
    private PART_INFMapper partInfMapper;
    @Resource
    private JOB_DATMapper jobDatMapper;
    @Resource
    private PRCS_INFMapper prcsInfMapper;
    @Resource
    private LOT_INFMapper lotInfMapper;
    @Resource
    private ChartCommonService chartCommonService;
    @Resource
    private INSPECTION_TYPE_DATMapper inspectionTypeDatMapper;
    @Resource
    private PART_REVMapper partRevMapper;

    @Override
    public List<SamplingSummaryDataDTO> getInfo(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        AnalysisChartConfigDTO analysisChartConfigDTO = analysisDashboardTemplateInfService.findByAnalysisChartConfig(subgroupDataSelectionDTO, ChartTypeEnum.SAMPLING_SUMMARY);

        BoxPlotsConfigDTO boxPlotsConfigDTO = new BoxPlotsConfigDTO();
        if (subgroupDataSelectionDTO.getBoxPlotsConfigDTO() != null) {
            boxPlotsConfigDTO = JSONObject.parseObject(JSONObject.toJSONString(subgroupDataSelectionDTO.getBoxPlotsConfigDTO()), BoxPlotsConfigDTO.class);
        } else {
            Object chartConfig = analysisChartConfigDTO.getChartConfig();
            boxPlotsConfigDTO = JSONObject.parseObject(JSONObject.toJSONString(chartConfig), BoxPlotsConfigDTO.class);
        }

        /*获取聚合分析子组数据*/
        subgroupDataSelectionDTO = chartCommonService.getCacheCondition(subgroupDataSelectionDTO, analysisChartConfigDTO);
        if (subgroupDataSelectionDTO == null)
            return new ArrayList<>();

        List<SubgroupDataDTO> subgroupDataDTOList = chartCommonService.getCacheSubgroupData(subgroupDataSelectionDTO, analysisChartConfigDTO);
        subgroupDataDTOList = subgroupDataDTOList.stream().filter(s -> s.getF_FLAG() != null && s.getF_FLAG() != YesOrNoEnum.YES.getType()).collect(Collectors.toList());

    //    subgroupDataDTOList = chartCommonService.getTotalNumSubgroup(subgroupDataDTOList, subgroupDataSelectionDTO.getMaxNum(),2);

        subgroupDataDTOList = chartCommonService.reassembly(subgroupDataDTOList);
        return getData(subgroupDataDTOList, boxPlotsConfigDTO, subgroupDataSelectionDTO.getParameterId(), analysisChartConfigDTO);
    }


    public List<SamplingSummaryDataDTO> getData(List<SubgroupDataDTO> sgrpExtList, BoxPlotsConfigDTO boxPlotsConfigDTO, Long parameterId, AnalysisChartConfigDTO analysisChartConfigDTO) {
        if (boxPlotsConfigDTO == null) {
            boxPlotsConfigDTO = BoxPlotsConfigDTO.initList();
        }
        Map<Integer, AnalyseTypeDTO> analyseTypeMap = boxPlotsConfigDTO.getAnalyseTypeMap();

        List<SamplingSummaryDataDTO> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(sgrpExtList)) return list;
        AnalyseTypeDTO levelOne = analyseTypeMap.get(1);

        if (levelOne == null)
            throw new BaseException(I18nUtils.getMessage("HIERARCHY_PARSING_ERROR"));

        Map<Long, List<SubgroupDataDTO>> oneMap = getList(sgrpExtList, levelOne);

        BoxPlotsConfigDTO finalBoxPlotsConfigDTO = boxPlotsConfigDTO;
        oneMap.forEach((k, v) -> {
            DataSummaryDTO dataSummaryDTO = getDataSummaryDTO(v, analysisChartConfigDTO);
            SamplingSummaryDataDTO samplingSummaryDataDTO = new SamplingSummaryDataDTO();
            samplingSummaryDataDTO.setLevel(1);
            v.sort(Comparator.comparing(SubgroupDataDTO::getF_SGTM).thenComparing(SubgroupDataDTO::getF_SGRP));
            samplingSummaryDataDTO.setStartDate(v.get(0).getF_SGTM());
            samplingSummaryDataDTO.setEndDate(v.get(v.size() - 1).getF_SGTM());
            samplingSummaryDataDTO.setDataSummaryDTO(dataSummaryDTO);
            getSamplingSummaryDataDTO(samplingSummaryDataDTO, finalBoxPlotsConfigDTO, levelOne, k, v, analysisChartConfigDTO);
            list.add(samplingSummaryDataDTO);
        });
        list.sort(new Comparator<SamplingSummaryDataDTO>() {
            public int compare(SamplingSummaryDataDTO o1, SamplingSummaryDataDTO o2) {
                String s1 = o1.getName();
                String s2 = o2.getName();
                //该排序为正序排序，如果倒序排序则将compare中的s2和s1互换位置
                return Collator.getInstance(Locale.UK).compare(s1, s2);
            }
        });

        return list;
    }

    public DataSummaryDTO getDataSummaryDTO(List<SubgroupDataDTO> subgroupDataDTOList, AnalysisChartConfigDTO analysisChartConfigDTO) {
        Map<String, List<SubgroupDataDTO>> map = subgroupDataDTOList.stream()
                .collect(Collectors.groupingBy(s -> s.getF_PART() + Constants.COMMA + s.getF_PRCS() + Constants.COMMA + s.getF_TEST(),
                        LinkedHashMap::new, Collectors.toList()));
        List<DataSummaryDTO> dataSummaryDTOList = new ArrayList<>();
        HashSet<SPEC_INF_DTO> specInfDtoSet = new HashSet<>();
        map.forEach((k, v) -> {
            SubgroupDataDTO subgroupDataDTO = v.get(0);
            DataSummaryDTO dataSummaryDTO = DataSummaryDTO.getBasic(v, analysisChartConfigDTO.getType(), 0);
            /*获取公差限数据  目标cp和目标cpk*/
            SPEC_INF_DTO specLim = specInfService.getSpecLim(dataSummaryDTO, subgroupDataDTO);
            specInfDtoSet.add(specLim);
            /*获取目标和超公差数据*/
            DataSummaryDTO.getBasicTwo(dataSummaryDTO);
            /*获取过程潜力指数*/
            DataSummaryDTO.getBasicThree(dataSummaryDTO);
            /*获取过程能力指数*/
            DataSummaryDTO.getBasicFour(dataSummaryDTO);
            dataSummaryDTOList.add(dataSummaryDTO);
        });

        /*求每个值的均值*/
        DataSummaryDTO dataSummaryDTO = new DataSummaryDTO();
        if (CollectionUtils.isNotEmpty(specInfDtoSet) && specInfDtoSet.size() == 1 && CollectionUtils.isNotEmpty(dataSummaryDTOList) && dataSummaryDTOList.size() == 1) {
            dataSummaryDTO = dataSummaryDTOList.get(0);
        } else {
            dataSummaryDTO.setTestNum(dataSummaryDTOList.stream().map(DataSummaryDTO::getTestNum).filter(Objects::nonNull).mapToInt(Integer::intValue).sum());
            dataSummaryDTO.setSubGroupNum(dataSummaryDTOList.stream().map(DataSummaryDTO::getSubGroupNum).filter(Objects::nonNull).mapToInt(Integer::intValue).sum());
            dataSummaryDTO.setSubGroupSize(dataSummaryDTOList.stream().map(DataSummaryDTO::getSubGroupSize).filter(Objects::nonNull).mapToDouble(Double::doubleValue).sum());
            dataSummaryDTO.setMax(dataSummaryDTOList.stream().map(DataSummaryDTO::getMax).filter(Objects::nonNull).mapToDouble(Double::doubleValue).average().orElse(0d));
            dataSummaryDTO.setMin(dataSummaryDTOList.stream().map(DataSummaryDTO::getMin).filter(Objects::nonNull).mapToDouble(Double::doubleValue).average().orElse(0d));
            dataSummaryDTO.setMean(dataSummaryDTOList.stream().map(DataSummaryDTO::getMean).filter(Objects::nonNull).mapToDouble(Double::doubleValue).average().orElse(0d));
            dataSummaryDTO.setShortTermStandardDeviation(dataSummaryDTOList.stream().map(DataSummaryDTO::getShortTermStandardDeviation).filter(Objects::nonNull).mapToDouble(Double::doubleValue).average().orElse(0d));
            dataSummaryDTO.setLongTermStandardDeviation(dataSummaryDTOList.stream().map(DataSummaryDTO::getLongTermStandardDeviation).filter(Objects::nonNull).mapToDouble(Double::doubleValue).average().orElse(0d));
            dataSummaryDTO.setCp(dataSummaryDTOList.stream().map(DataSummaryDTO::getCp).filter(Objects::nonNull).mapToDouble(Double::doubleValue).average().orElse(0d));
            dataSummaryDTO.setCpk(dataSummaryDTOList.stream().map(DataSummaryDTO::getCpk).filter(Objects::nonNull).mapToDouble(Double::doubleValue).average().orElse(0d));
            dataSummaryDTO.setPp(dataSummaryDTOList.stream().map(DataSummaryDTO::getPp).filter(Objects::nonNull).mapToDouble(Double::doubleValue).average().orElse(0d));
            dataSummaryDTO.setPpk(dataSummaryDTOList.stream().map(DataSummaryDTO::getPpk).filter(Objects::nonNull).mapToDouble(Double::doubleValue).average().orElse(0d));
            dataSummaryDTO.setUslPercent(dataSummaryDTOList.stream().map(DataSummaryDTO::getUslPercent).filter(Objects::nonNull).mapToDouble(Double::doubleValue).average().orElse(0d));
            dataSummaryDTO.setLslPercent(dataSummaryDTOList.stream().map(DataSummaryDTO::getLslPercent).filter(Objects::nonNull).mapToDouble(Double::doubleValue).average().orElse(0d));
            dataSummaryDTO.setActualValueTotal(dataSummaryDTOList.stream().map(DataSummaryDTO::getActualValueTotal).filter(Objects::nonNull).mapToDouble(Double::doubleValue).average().orElse(0d));
            dataSummaryDTO.setActualPpm(dataSummaryDTOList.stream().map(DataSummaryDTO::getActualPpm).filter(Objects::nonNull).mapToDouble(Double::doubleValue).average().orElse(0d));
        }
        return dataSummaryDTO;
    }

    public static void getChildData(List<BoxPlotsDataDTO> list, List<BoxPlotsParticularsDTO> childList) {
        for (BoxPlotsDataDTO plotsDataDTO : list) {
            if (plotsDataDTO.getParticulars() != null) {
                childList.add(plotsDataDTO.getParticulars());
            }
            if (CollectionUtils.isNotEmpty(plotsDataDTO.getBoxPlotsDataChildList())) {
                getChildData(plotsDataDTO.getBoxPlotsDataChildList(), childList);
            }
        }
    }

    /**
     * 递归
     *
     * @param samplingSummaryDataDTO
     * @param boxPlotsConfigDTO
     * @return
     */
    public void initData(SamplingSummaryDataDTO samplingSummaryDataDTO, BoxPlotsConfigDTO boxPlotsConfigDTO, List<SubgroupDataDTO> extList,
                         List<SamplingSummaryDataDTO> list, AnalysisChartConfigDTO analysisChartConfigDTO) {
        Map<Integer, AnalyseTypeDTO> analyseTypeMap = boxPlotsConfigDTO.getAnalyseTypeMap();
        AnalyseTypeDTO analyseTypeDTO = analyseTypeMap.get(samplingSummaryDataDTO.getLevel());
        if (analyseTypeDTO == null) {
            return;
        }

        Map<Long, List<SubgroupDataDTO>> map = getList(extList, analyseTypeDTO);
        map.forEach((k, v) -> {
            SamplingSummaryDataDTO samplingSummaryData = new SamplingSummaryDataDTO();
            DataSummaryDTO dataSummaryDTO = getDataSummaryDTO(v, analysisChartConfigDTO);
            samplingSummaryData.setLevel(samplingSummaryDataDTO.getLevel());
            samplingSummaryData.setDataSummaryDTO(dataSummaryDTO);
            v.sort(Comparator.comparing(SubgroupDataDTO::getF_SGTM).thenComparing(SubgroupDataDTO::getF_SGRP));
            samplingSummaryData.setStartDate(v.get(0).getF_SGTM());
            samplingSummaryData.setEndDate(v.get(v.size() - 1).getF_SGTM());
            SamplingSummaryDataDTO dataDTO = getSamplingSummaryDataDTO(samplingSummaryData, boxPlotsConfigDTO, analyseTypeDTO, k, v, analysisChartConfigDTO);
            list.add(dataDTO);
        });


    }

    private SamplingSummaryDataDTO getSamplingSummaryDataDTO(SamplingSummaryDataDTO samplingSummaryDataDTO, BoxPlotsConfigDTO boxPlotsConfigDTO,
                                                             AnalyseTypeDTO level, Long k, List<SubgroupDataDTO> v, AnalysisChartConfigDTO analysisChartConfigDTO) {
        SamplingSummaryDataDTO dataDTO = new SamplingSummaryDataDTO();
        dataDTO.setName("不存在");
        dataDTO.setLevel(samplingSummaryDataDTO.getLevel() + 1);
        Map<Integer, AnalyseTypeDTO> analyseTypeMap = boxPlotsConfigDTO.getAnalyseTypeMap();
        AnalyseTypeDTO analyseTypeDTO = analyseTypeMap.get(samplingSummaryDataDTO.getLevel());
        samplingSummaryDataDTO.setName(getName(analyseTypeDTO, k));

        List<SamplingSummaryDataDTO> list = new ArrayList<>();
        initData(dataDTO, boxPlotsConfigDTO, v, list, analysisChartConfigDTO);
        list.sort(new Comparator<SamplingSummaryDataDTO>() {
            public int compare(SamplingSummaryDataDTO o1, SamplingSummaryDataDTO o2) {
                String s1 = o1.getName();
                String s2 = o2.getName();
                //该排序为正序排序，如果倒序排序则将compare中的s2和s1互换位置
                return Collator.getInstance(Locale.UK).compare(s1, s2);
            }
        });
        samplingSummaryDataDTO.setSamplingSummaryDataChildList(list);
        return samplingSummaryDataDTO;
    }

    public Map<Long, List<SubgroupDataDTO>> getList(List<SubgroupDataDTO> subgroupDataDTOList, AnalyseTypeDTO analyseTypeDTO) {
        Map<Long, List<SubgroupDataDTO>> map = new HashMap<>();
        switch (SamplingSummaryAnalyseTypeEnum.getByCode(analyseTypeDTO.getAnalyseType())) {
            case SHIFT_DAT:
                map = subgroupDataDTOList.stream().filter(s -> s.getF_SHIFT() != null && s.getF_SHIFT() != 0L)
                        .collect(Collectors.groupingBy(SubgroupDataDTO::getF_SHIFT,
                                LinkedHashMap::new, Collectors.toList()));
                break;
            case TEST_DAT:
                map = subgroupDataDTOList.stream().filter(s -> s.getF_TEST() != null && s.getF_TEST() != 0L)
                        .collect(Collectors.groupingBy(SubgroupDataDTO::getF_TEST,
                                LinkedHashMap::new, Collectors.toList()));
                break;
            case PART_DAT:
                map = subgroupDataDTOList.stream().filter(s -> s.getF_PART() != null)
                        .collect(Collectors.groupingBy(SubgroupDataDTO::getF_PART,
                                LinkedHashMap::new, Collectors.toList()));
                break;
            case JOB_DAT:
                map = subgroupDataDTOList.stream().filter(s -> s.getF_JOB() != null)
                        .collect(Collectors.groupingBy(SubgroupDataDTO::getF_JOB,
                                LinkedHashMap::new, Collectors.toList()));
                break;
            case PRCS_DAT:
                map = subgroupDataDTOList.stream().filter(s -> s.getF_PRCS() != null)
                        .collect(Collectors.groupingBy(SubgroupDataDTO::getF_PRCS,
                                LinkedHashMap::new, Collectors.toList()));
                break;
            case LOT_DAT:
                map = subgroupDataDTOList.stream().filter(s -> s.getF_LOT() != null)
                        .collect(Collectors.groupingBy(SubgroupDataDTO::getF_LOT,
                                LinkedHashMap::new, Collectors.toList()));
                break;
            case INSPECTION_TYPE:
                map = subgroupDataDTOList.stream().filter(s -> s.getF_INSPECTION_TYPE() != null)
                        .collect(Collectors.groupingBy(SubgroupDataDTO::getF_INSPECTION_TYPE,
                                LinkedHashMap::new, Collectors.toList()));
            case PTRV_DAT:
                map = subgroupDataDTOList.stream().filter(s -> s.getF_REV() != null)
                        .collect(Collectors.groupingBy(SubgroupDataDTO::getF_REV,
                                LinkedHashMap::new, Collectors.toList()));
        }
        return map;
    }


    public String getName(AnalyseTypeDTO analyseTypeDTO, Long k) {
        String name = "不存在";
        switch (SamplingSummaryAnalyseTypeEnum.getByCode(analyseTypeDTO.getAnalyseType())) {
            case SHIFT_DAT:
                SHIFT_DAT shftDat = shiftDatMapper.selectById(k);
                if (shftDat != null) name = shftDat.getF_NAME();
                break;
            case TEST_DAT:
                TEST_INF testDat = testInfMapper.selectById(k);
                if (testDat != null) name = testDat.getF_NAME();
                break;
            case PART_DAT:
                PART_INF partDat = partInfMapper.selectById(k);
                if (partDat != null) name = partDat.getF_NAME();
                break;
            case JOB_DAT:
                JOB_DAT jobDat = jobDatMapper.selectById(k);
                if (jobDat != null) name = jobDat.getF_NAME();
                break;
            case PRCS_DAT:
                PRCS_INF prcsDat = prcsInfMapper.selectById(k);
                if (prcsDat != null) name = prcsDat.getF_NAME();
                break;
            case LOT_DAT:
                LOT_INF partLot = lotInfMapper.selectById(k);
                if (partLot != null) name = partLot.getF_NAME();
                break;
            case INSPECTION_TYPE:
                INSPECTION_TYPE_DAT inspectionTypeDat = inspectionTypeDatMapper.selectById(k);
                if (inspectionTypeDat != null) name = inspectionTypeDat.getF_NAME();
                break;
            case PTRV_DAT:
                PART_REV partRev = partRevMapper.selectById(k);
                if (partRev != null) name = partRev.getF_NAME();
                break;
        }
        return name;
    }
}
