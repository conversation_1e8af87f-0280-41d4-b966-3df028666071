# Tomcat
server:
  port: 9210

profile: ${ENV:pgsql}

# Spring
spring:
  application:
    # 应用名称
    name: yingfei-system
  main:
    allow-circular-references: true
  cloud:
    nacos:
      common:
        # 服务注册地址
        server-addr: 192.168.2.13:8848
        # 命名空间
        namespace: qdd-test
        #用户名
        username: nacos
        #密码
        password: nacos
      discovery:
        server-addr: ${spring.cloud.nacos.common.server-addr}
        namespace: ${spring.cloud.nacos.common.namespace}
        username: ${spring.cloud.nacos.common.username}
        password: ${spring.cloud.nacos.common.password}
      config:
        server-addr: ${spring.cloud.nacos.common.server-addr}
        namespace: ${spring.cloud.nacos.common.namespace}
        username: ${spring.cloud.nacos.common.username}
        password: ${spring.cloud.nacos.common.password}
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - data-id: datasource-${profile}.yml
            group: DEFAULT_GROUP
            refresh: true
        # 应用特定配置
        extension-configs:
          - data-id: yingfei-auth.yml
            group: DEFAULT_GROUP
            refresh: true
